"""Utilities for detach() operations and gradient control.

This module provides gradient blocking utilities essential for DELTA's
dual-objective gradient learning mechanism, where Thesis and Antithesis
components receive gradients from different loss functions.
"""

import torch
from torch import Tensor
from torch.autograd import Function
from typing import Optional, Callable, Tuple, Any
import warnings


def detach_tensor(tensor: Tensor) -> Tensor:
    """Detach tensor from computation graph.
    
    This is the basic operation used in dual-objective learning to prevent
    gradient flow from the local loss to the Antithesis components.
    
    Args:
        tensor: Input tensor
        
    Returns:
        Detached tensor
    """
    return tensor.detach()


def selective_detach(
    tensor: Tensor,
    mask: Optional[Tensor] = None,
    detach_ratio: float = 1.0
) -> Tensor:
    """Selectively detach parts of a tensor.
    
    Per DeltaDecoder.md Section 5, supports selective gradient blocking
    for dual-objective learning.
    
    Args:
        tensor: Input tensor
        mask: Boolean mask for selective detachment (broadcasts to tensor shape)
        detach_ratio: Ratio of elements to detach (if no mask)
        
    Returns:
        Partially detached tensor
    """
    if mask is not None:
        # Ensure mask can broadcast with tensor
        while mask.dim() < tensor.dim():
            mask = mask.unsqueeze(-1)
        
        # Detach only masked elements
        detached = tensor.detach()
        return torch.where(mask, detached, tensor)
    elif detach_ratio < 1.0:
        # Randomly detach elements
        mask = torch.rand_like(tensor) < detach_ratio
        detached = tensor.detach()
        return torch.where(mask, detached, tensor)
    else:
        return tensor.detach()


class GradientReversal(Function):
    """Gradient reversal layer for adversarial training."""
    
    @staticmethod
    def forward(ctx, x: Tensor, lambda_: float = 1.0) -> Tensor:
        ctx.lambda_ = lambda_
        return x.view_as(x)
    
    @staticmethod
    def backward(ctx, grad_output: Tensor) -> Tuple[Tensor, None]:
        return grad_output.neg() * ctx.lambda_, None


def gradient_reversal(x: Tensor, lambda_: float = 1.0) -> Tensor:
    """Apply gradient reversal.
    
    Args:
        x: Input tensor
        lambda_: Scaling factor for reversed gradient
        
    Returns:
        Tensor with reversed gradient
    """
    return GradientReversal.apply(x, lambda_)


class GradientScaling(Function):
    """Scale gradients during backpropagation."""
    
    @staticmethod
    def forward(ctx, x: Tensor, scale: float) -> Tensor:
        ctx.scale = scale
        return x.view_as(x)
    
    @staticmethod
    def backward(ctx, grad_output: Tensor) -> Tuple[Tensor, None]:
        return grad_output * ctx.scale, None


def scale_gradient(x: Tensor, scale: float) -> Tensor:
    """Scale gradient during backpropagation.
    
    Args:
        x: Input tensor
        scale: Gradient scaling factor
        
    Returns:
        Tensor with scaled gradient
    """
    return GradientScaling.apply(x, scale)


class StopGradient(Function):
    """Stop gradient flow completely (stronger than detach)."""
    
    @staticmethod
    def forward(ctx, x: Tensor) -> Tensor:
        return x.view_as(x)
    
    @staticmethod
    def backward(ctx, grad_output: Tensor) -> Tensor:
        return torch.zeros_like(grad_output)


def stop_gradient(x: Tensor) -> Tensor:
    """Completely stop gradient flow.
    
    Args:
        x: Input tensor
        
    Returns:
        Tensor with zero gradient
    """
    return StopGradient.apply(x)


def conditional_detach(
    tensor: Tensor,
    condition,  # Can be bool, callable, or tensor condition
    warning_msg: Optional[str] = None
) -> Tensor:
    """Conditionally detach tensor based on a condition.
    
    Args:
        tensor: Input tensor
        condition: Boolean, callable returning boolean, or condition to evaluate
        warning_msg: Optional warning message
        
    Returns:
        Conditionally detached tensor
    """
    # Evaluate condition if it's callable
    if callable(condition):
        should_detach = condition(tensor)
    else:
        should_detach = condition
    
    if should_detach:
        if warning_msg:
            warnings.warn(warning_msg)
        return tensor.detach()
    return tensor


class DualPathGradient(torch.nn.Module):
    """Module for dual-path gradient flow as in DELTA.
    
    This implements the core mechanism where different components
    receive gradients from different loss functions.
    """
    
    def __init__(self):
        super().__init__()
        self.register_buffer('gradient_mask', None)
    
    def forward(
        self,
        thesis: Tensor,
        antithesis: Tensor,
        synthesis_fn: Callable[[Tensor, Tensor], Tensor]
    ) -> Tensor:
        """Apply synthesis with proper gradient blocking.
        
        Args:
            thesis: Thesis tensor (receives local gradients)
            antithesis: Antithesis tensor (receives global gradients)
            synthesis_fn: Function to synthesize thesis and antithesis
            
        Returns:
            Synthesis result with proper gradient paths
        """
        # Detach antithesis for local loss computation
        antithesis_detached = antithesis.detach()
        
        # Compute synthesis
        synthesis = synthesis_fn(thesis, antithesis_detached)
        
        return synthesis


def gradient_checkpoint(
    module: torch.nn.Module,
    *args,
    use_reentrant: bool = False,
    **kwargs
) -> Any:
    """Checkpoint module computation for memory efficiency.
    
    Args:
        module: Module to checkpoint
        *args: Positional arguments for module
        use_reentrant: Whether to use reentrant checkpointing
        **kwargs: Keyword arguments for module
        
    Returns:
        Module output
    """
    if torch.is_grad_enabled():
        return torch.utils.checkpoint.checkpoint(
            module,
            *args,
            use_reentrant=use_reentrant,
            **kwargs
        )
    else:
        return module(*args, **kwargs)


class GradientClipping(torch.nn.Module):
    """Module wrapper for gradient clipping."""
    
    def __init__(
        self,
        module: torch.nn.Module,
        max_norm: float = 1.0,
        norm_type: float = 2.0
    ):
        super().__init__()
        self.module = module
        self.max_norm = max_norm
        self.norm_type = norm_type
        
        # Register gradient clipping hook
        for param in self.module.parameters():
            param.register_hook(self._clip_grad)
    
    def _clip_grad(self, grad: Tensor) -> Tensor:
        """Clip gradient norm."""
        norm = grad.norm(self.norm_type)
        if norm > self.max_norm:
            return grad * (self.max_norm / norm)
        return grad
    
    def forward(self, *args, **kwargs):
        return self.module(*args, **kwargs)


def create_gradient_mask(
    shape: Tuple[int, ...],
    sparsity: float = 0.5,
    device: torch.device = None
) -> Tensor:
    """Create a mask for sparse gradient updates.
    
    Args:
        shape: Shape of the mask
        sparsity: Fraction of gradients to block
        device: Device to create mask on
        
    Returns:
        Binary gradient mask
    """
    if device is None:
        device = torch.device('cpu')
    
    mask = torch.rand(shape, device=device) > sparsity
    return mask.float()


class AsymmetricGradient(Function):
    """Apply different operations in forward and backward pass."""
    
    @staticmethod
    def forward(ctx, x: Tensor, forward_fn: Callable, backward_fn: Callable) -> Tensor:
        ctx.backward_fn = backward_fn
        ctx.save_for_backward(x)
        return forward_fn(x)
    
    @staticmethod
    def backward(ctx, grad_output: Tensor) -> Tuple[Tensor, None, None]:
        x, = ctx.saved_tensors
        grad_input = ctx.backward_fn(x, grad_output)
        return grad_input, None, None


def asymmetric_operation(
    x: Tensor,
    forward_fn: Callable[[Tensor], Tensor],
    backward_fn: Callable[[Tensor, Tensor], Tensor]
) -> Tensor:
    """Apply different operations in forward and backward.
    
    Args:
        x: Input tensor
        forward_fn: Function for forward pass
        backward_fn: Function for backward pass (receives x and grad_output)
        
    Returns:
        Result with asymmetric gradient
    """
    return AsymmetricGradient.apply(x, forward_fn, backward_fn)


def dual_objective_update(
    thesis_params: torch.nn.ParameterList,
    antithesis_params: torch.nn.ParameterList,
    local_loss: Tensor,
    global_loss: Tensor,
    thesis_lr: float = 1e-3,
    antithesis_lr: float = 1e-3
) -> None:
    """Update parameters according to dual-objective learning.
    
    This implements the core DELTA learning rule where:
    - Thesis parameters are updated by local loss
    - Antithesis parameters are updated by global loss
    
    Args:
        thesis_params: Parameters for thesis generation
        antithesis_params: Parameters for antithesis generation
        local_loss: Local tension loss
        global_loss: Global task loss
        thesis_lr: Learning rate for thesis
        antithesis_lr: Learning rate for antithesis
    """
    # Compute gradients for local loss (thesis only)
    local_loss.backward(retain_graph=True)
    
    # Update thesis parameters
    with torch.no_grad():
        for param in thesis_params:
            if param.grad is not None:
                param.add_(param.grad, alpha=-thesis_lr)
                param.grad.zero_()
    
    # Compute gradients for global loss (antithesis only)
    global_loss.backward()
    
    # Update antithesis parameters
    with torch.no_grad():
        for param in antithesis_params:
            if param.grad is not None:
                param.add_(param.grad, alpha=-antithesis_lr)
                param.grad.zero_()


class GradientModulation(torch.nn.Module):
    """Modulate gradients based on a gating signal."""
    
    def __init__(self, d_model: int):
        super().__init__()
        self.gate = torch.nn.Parameter(torch.ones(d_model))
    
    def forward(self, x: Tensor) -> Tensor:
        """Apply gradient modulation.
        
        In forward pass: x * gate
        In backward pass: gradient * gate
        """
        return x * self.gate