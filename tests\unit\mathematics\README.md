# DELTA Architecture Mathematical Test Suite

This directory contains comprehensive tests for all mathematical operations in the DELTA (Dialectic Evaluative Layered Tension Attractor) architecture, ensuring strict adherence to the specification documented in `DeltaDecoder.md`.

## Overview

The DELTA architecture implements a sophisticated three-phase dialectical learning process:

1. **Thesis Generation**: Standard transformer processing
2. **Antithesis Formation**: Structural and semantic force computation  
3. **Synthesis**: FFT-based harmonic synthesis of thesis and antithesis

These tests validate that every mathematical operation follows the exact equations specified in the DELTA architecture document.

## Test Structure

### Core Test Files

#### 1. `test_fft_operations.py`
Tests the core synthesis operation: **V_S,raw = real(iFFT(FFT(V_T) ⊙ FFT(κ)))**

- **Purpose**: Validate Stage 3 synthesis equation compliance
- **Key Tests**:
  - Exact implementation of specification equation
  - Convolution theorem compliance
  - Circular convolution properties
  - Frequency domain operations
  - Spectral windowing and filtering

#### 2. `test_attention_mechanisms.py`  
Tests κ-Cache attention: **α = softmax(K_i @ V_T,new / sqrt(d))**, **κ_context = α^T @ K_i**

- **Purpose**: Validate linear complexity attention mechanism
- **Key Tests**:
  - Exact attention weight computation
  - Context retrieval equation compliance
  - Multi-head attention scaling
  - Attention probability properties
  - Memory efficiency validation

#### 3. `test_activation_functions.py`
Tests activation functions used throughout DELTA blocks

- **Purpose**: Validate activation function implementations
- **Key Tests**:
  - Standard activations (GELU, Swish, Mish)
  - Gated activations (SwiGLU, GLU variants)
  - Adaptive activation mechanisms
  - Gradient flow through activations
  - Numerical stability

#### 4. `test_normalization_operations.py`
Tests LayerNorm in refinement: **h' = LayerNorm(V_T + V_S,raw)**, **V_S = LayerNorm(h' + FFN(h'))**

- **Purpose**: Validate Stage 4 refinement operations
- **Key Tests**:
  - LayerNorm mathematical properties
  - Zero mean and unit variance verification
  - Refinement sequence compliance
  - Residual connection handling
  - Numerical stability

#### 5. `test_tensor_operations.py`
Tests utility tensor operations for robust DELTA processing

- **Purpose**: Validate supporting tensor manipulations
- **Key Tests**:
  - Safe concatenation for antithesis fusion
  - Tensor splitting and reshaping
  - Masked operations for variable sequences
  - Causal masking generation
  - Shape preservation

#### 6. `test_advanced_operations.py`
Tests gradient blocking and numerical stability: **||V_T - detach(κ_effective)||²**

- **Purpose**: Validate dual-objective learning mechanism
- **Key Tests**:
  - Gradient blocking for dual objectives
  - Local tension loss computation
  - Numerical stability measures
  - Safe mathematical operations
  - Optimization robustness

### Test Suite Orchestration

#### `test_math_suite.py`
Comprehensive test runner that validates specification compliance across all components.

- **Specification Compliance Tests**: Validates exact equation implementation
- **Property Verification**: Ensures mathematical properties are preserved
- **Edge Case Handling**: Tests robustness under extreme conditions
- **Performance Metrics**: Measures computational efficiency
- **Coverage Reporting**: Generates detailed compliance reports

#### `conftest.py`
Pytest configuration providing fixtures and utilities for consistent testing.

- **Test Fixtures**: Common tensors, devices, and configurations
- **Assertion Helpers**: DELTA-specific property validators
- **Performance Measurement**: Timing and efficiency tracking
- **Random Seed Management**: Reproducible test results

## Running Tests

### Run All Mathematical Tests
```bash
cd tests/unit/mathematics
python -m pytest -v
```

### Run Specification Compliance Suite
```bash
python test_math_suite.py
```

### Run Specific Test Categories
```bash
# FFT operations only
python -m pytest -v -m fft

# Attention mechanisms only  
python -m pytest -v -m attention

# Gradient-related tests only
python -m pytest -v -m gradient

# Numerical stability tests
python -m pytest -v -m numerical_stability
```

### Run with Coverage Report
```bash
python -m pytest -v --cov=source.math --cov-report=html
```

## Specification Compliance

### Core Equations Tested

1. **Stage 3 Synthesis**: `V_S,raw = real(iFFT(FFT(V_T) ⊙ FFT(κ)))`
2. **κ-Cache Attention**: `α = softmax(K_i @ V_T,new / sqrt(d))`
3. **Context Retrieval**: `κ_context = α^T @ K_i`
4. **Refinement**: `h' = LayerNorm(V_T + V_S,raw)`
5. **Local Tension**: `||V_T - detach(κ_effective)||²`
6. **Antithesis Fusion**: `κ_effective = W_κ[κ_new; κ_context] + b_κ`

### Algorithm 1 Components Validated

- **Line 2**: Thesis generation `V_T ← W_T h_in + b_T`
- **Lines 4-6**: Gating computation for structural forces
- **Lines 8-12**: Structural forces using relative positions
- **Lines 14-19**: Semantic forces from κ-Cache attention
- **Line 21**: Composite antithesis `κ ← κ_Struct + κ_Sem`
- **Line 23**: FFT synthesis operation
- **Lines 25-28**: Refinement and state update

### Mathematical Properties Verified

- **Convolution Theorem**: FFT synthesis follows convolution properties
- **Attention Weights**: Sum to 1 and remain non-negative
- **LayerNorm**: Produces zero mean and unit variance
- **Gradient Flow**: Correct backpropagation through all operations
- **Numerical Stability**: Robust handling of edge cases
- **Shape Preservation**: Tensor dimensions maintained throughout

## Test Organization Principles

### Test-Driven Development (TDD)
All tests follow TDD principles:
1. **Red**: Write failing test for specification requirement
2. **Green**: Implement minimal code to pass test
3. **Refactor**: Optimize while maintaining test passage

### Specification-First Testing
Tests are derived directly from the DELTA specification:
- Each equation has corresponding validation
- Mathematical properties are explicitly verified
- Edge cases are systematically covered

### Comprehensive Coverage
Tests ensure complete coverage of:
- **Functional Correctness**: Operations produce expected results
- **Mathematical Properties**: Theoretical requirements are met
- **Numerical Stability**: Robust handling of problematic inputs
- **Gradient Flow**: Backpropagation works correctly
- **Performance**: Operations are computationally efficient

## Dependencies

The test suite requires:
- **PyTorch**: Core tensor operations and autograd
- **NumPy**: Numerical computations and reference implementations
- **Pytest**: Test framework and fixtures
- **Math**: Standard mathematical functions

## Continuous Integration

Tests are designed for CI/CD pipelines:
- **Deterministic**: Fixed random seeds ensure reproducible results
- **Fast Execution**: Efficient test design minimizes runtime
- **Clear Reporting**: Detailed output for debugging failures
- **Modular**: Individual components can be tested independently

## Contributing

When adding new mathematical operations to DELTA:

1. **Specification First**: Document the mathematical equation
2. **Test First**: Write tests validating the specification
3. **Implementation**: Implement the operation to pass tests
4. **Integration**: Add to the comprehensive test suite
5. **Documentation**: Update this README with new test information

## Troubleshooting

### Common Issues

**Import Errors**: Ensure `source` directory is in Python path
```bash
export PYTHONPATH="${PYTHONPATH}:/path/to/delta/source"
```

**CUDA Errors**: Tests automatically detect and use available devices
```bash
# Force CPU testing
CUDA_VISIBLE_DEVICES="" python -m pytest
```

**Numerical Precision**: Adjust tolerances in `conftest.py` if needed
```python
test_config = {
    'rtol': 1e-5,  # Relative tolerance
    'atol': 1e-6,  # Absolute tolerance
}
```

### Performance Issues

For slow tests, use parallel execution:
```bash
python -m pytest -n auto  # Requires pytest-xdist
```

Skip slow tests during development:
```bash
python -m pytest -m "not slow"
```

## Test Results Interpretation

### Success Criteria
- **95%+ Success Rate**: Excellent specification compliance
- **90%+ Success Rate**: Good compliance with minor issues
- **80%+ Success Rate**: Acceptable but needs improvement
- **<80% Success Rate**: Significant compliance issues requiring attention

### Report Generation
The test suite generates detailed reports including:
- **Equation Compliance**: Verification of each specification equation
- **Property Validation**: Mathematical property preservation
- **Performance Metrics**: Execution times and efficiency measures
- **Coverage Analysis**: Complete test coverage mapping

## Future Enhancements

Planned improvements to the test suite:
- **Property-Based Testing**: Hypothesis-driven random test generation
- **Benchmark Comparisons**: Performance against reference implementations
- **Visualization**: Graphical analysis of mathematical operations
- **Formal Verification**: Mathematical proof assistance integration

---

This comprehensive test suite ensures that the DELTA architecture implementation strictly adheres to the mathematical specification, providing confidence in the correctness and robustness of the dialectical learning system.
