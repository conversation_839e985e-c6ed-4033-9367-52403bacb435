"""
Comprehensive test suite for activation functions in DELTA architecture.

This module tests activation functions used throughout the DELTA blocks,
including standard activations and custom adaptive activations for dialectic processing.

Tests follow TDD principles and validate specification adherence.
"""

import pytest
import torch
import torch.nn.functional as F
import numpy as np
from typing import Tuple, Optional, Callable
import math

from source.delta.math.activations import (
    gelu,
    swish,
    mish,
    gated_linear_unit,
    swiglu,
    geglu,
    adaptive_activation,
    smooth_relu,
    hard_swish,
    hard_sigmoid,
    prelu_custom,
    elu_plus,
    softmax_temperature,
    sparsemax,
    gumbel_softmax,
    AdaptiveActivation,
    MixtureOfActivations,
    get_activation_fn
)


class TestStandardActivations:
    """Test standard activation functions for correctness and properties."""
    
    def setup_method(self):
        """Set up test fixtures."""
        torch.manual_seed(42)
        self.batch_size = 4
        self.d_model = 64
        self.input_tensor = torch.randn(self.batch_size, self.d_model)
        
        # Test edge cases
        self.zero_input = torch.zeros(self.batch_size, self.d_model)
        self.large_positive = torch.full((self.batch_size, self.d_model), 10.0)
        self.large_negative = torch.full((self.batch_size, self.d_model), -10.0)
    
    def test_gelu_correctness(self):
        """Test GELU activation matches reference implementation."""
        result = gelu(self.input_tensor)
        
        # Compare with PyTorch's built-in GELU (more accurate than tanh approximation)
        expected = F.gelu(self.input_tensor)
        
        torch.testing.assert_close(result, expected, rtol=1e-5, atol=1e-6)
    
    def test_gelu_properties(self):
        """Test GELU mathematical properties."""
        # GELU is smooth and differentiable
        x = self.input_tensor.requires_grad_(True)
        y = gelu(x)
        y.sum().backward()
        
        assert x.grad is not None
        assert torch.isfinite(x.grad).all()
        
        # GELU(0) ≈ 0
        zero_result = gelu(self.zero_input)
        assert torch.allclose(zero_result, torch.zeros_like(zero_result), atol=1e-6)
        
        # GELU is approximately identity for large positive values
        large_pos_result = gelu(self.large_positive)
        assert torch.allclose(large_pos_result, self.large_positive, rtol=1e-2)
        
        # GELU is approximately zero for large negative values
        large_neg_result = gelu(self.large_negative)
        assert torch.allclose(large_neg_result, torch.zeros_like(large_neg_result), atol=1e-3)
    
    def test_swish_correctness(self):
        """Test Swish activation: x * sigmoid(x)."""
        result = swish(self.input_tensor)
        expected = self.input_tensor * torch.sigmoid(self.input_tensor)
        
        torch.testing.assert_close(result, expected, rtol=1e-6, atol=1e-6)
    
    def test_swish_properties(self):
        """Test Swish mathematical properties."""
        # Swish(0) = 0
        zero_result = swish(self.zero_input)
        assert torch.allclose(zero_result, torch.zeros_like(zero_result), atol=1e-6)
        
        # Swish is smooth and non-monotonic (has a small dip for negative values)
        x = torch.linspace(-3, 3, 100).requires_grad_(True)
        y = swish(x)
        y.sum().backward()
        
        assert x.grad is not None
        assert torch.isfinite(x.grad).all()
    
    def test_mish_correctness(self):
        """Test Mish activation: x * tanh(softplus(x))."""
        result = mish(self.input_tensor)
        softplus = torch.log(1 + torch.exp(self.input_tensor))
        expected = self.input_tensor * torch.tanh(softplus)
        
        torch.testing.assert_close(result, expected, rtol=1e-5, atol=1e-6)
    
    def test_mish_properties(self):
        """Test Mish mathematical properties."""
        # Mish(0) ≈ 0
        zero_result = mish(self.zero_input)
        assert torch.allclose(zero_result, torch.zeros_like(zero_result), atol=1e-3)
        
        # Mish is smooth everywhere
        x = self.input_tensor.requires_grad_(True)
        y = mish(x)
        y.sum().backward()
        
        assert x.grad is not None
        assert torch.isfinite(x.grad).all()
    
    def test_smooth_relu_properties(self):
        """Test smooth ReLU properties."""
        result = smooth_relu(self.input_tensor)
        
        # Should be smooth (differentiable everywhere)
        x = self.input_tensor.requires_grad_(True)
        y = smooth_relu(x)
        y.sum().backward()
        
        assert x.grad is not None
        assert torch.isfinite(x.grad).all()
        
        # Should be approximately ReLU for large values
        large_pos_result = smooth_relu(self.large_positive)
        expected_large = torch.clamp(self.large_positive, min=0)
        assert torch.allclose(large_pos_result, expected_large, rtol=1e-2)
    
    def test_hard_swish_efficiency(self):
        """Test hard swish as efficient approximation of swish."""
        hard_result = hard_swish(self.input_tensor)
        swish_result = swish(self.input_tensor)
        
        # Should be similar to swish but more efficient to compute
        # The approximation should be reasonably close
        correlation = torch.corrcoef(torch.stack([
            hard_result.flatten(), swish_result.flatten()
        ]))[0, 1]
        
        assert correlation > 0.9  # High correlation expected
    
    def test_hard_sigmoid_properties(self):
        """Test hard sigmoid properties."""
        result = hard_sigmoid(self.input_tensor)
        
        # Output should be in [0, 1]
        assert (result >= 0).all()
        assert (result <= 1).all()
        
        # Should be approximately sigmoid for moderate values
        moderate_input = torch.randn(self.batch_size, self.d_model) * 2
        hard_result = hard_sigmoid(moderate_input)
        soft_result = torch.sigmoid(moderate_input)
        
        # Should be reasonably close
        mse = torch.mean((hard_result - soft_result) ** 2)
        assert mse < 0.1  # Reasonable approximation
    
    def test_activation_output_shapes(self):
        """Test that all activations preserve input shapes."""
        activations = [gelu, swish, mish, smooth_relu, hard_swish, hard_sigmoid]
        
        for activation in activations:
            result = activation(self.input_tensor)
            assert result.shape == self.input_tensor.shape
            assert torch.isfinite(result).all()
    
    def test_activation_numerical_stability(self):
        """Test numerical stability of activations."""
        # Test with extreme values
        extreme_inputs = [
            torch.full((2, 4), 100.0),    # Large positive
            torch.full((2, 4), -100.0),   # Large negative
            torch.full((2, 4), 1e-8),     # Very small positive
            torch.full((2, 4), -1e-8),    # Very small negative
        ]
        
        activations = [gelu, swish, mish, smooth_relu, hard_swish, hard_sigmoid]
        
        for activation in activations:
            for extreme_input in extreme_inputs:
                result = activation(extreme_input)
                assert torch.isfinite(result).all()
                assert not torch.isnan(result).any()


class TestGatedActivations:
    """Test gated activation functions used in DELTA architecture."""
    
    def setup_method(self):
        """Set up test fixtures."""
        torch.manual_seed(42)
        self.batch_size = 4
        self.d_model = 64
        self.input_tensor = torch.randn(self.batch_size, self.d_model)
    
    def test_gated_linear_unit(self):
        """Test Gated Linear Unit (GLU) activation."""
        # GLU expects input to be split in half
        input_doubled = torch.randn(self.batch_size, self.d_model * 2)
        result = gated_linear_unit(input_doubled)
        
        # Output should be half the input dimension
        assert result.shape == (self.batch_size, self.d_model)
        
        # Manual computation
        a, b = input_doubled.chunk(2, dim=-1)
        expected = a * torch.sigmoid(b)
        
        torch.testing.assert_close(result, expected, rtol=1e-6, atol=1e-6)
    
    def test_swiglu_activation(self):
        """Test SwiGLU activation function."""
        # SwiGLU expects input to be split in half
        input_doubled = torch.randn(self.batch_size, self.d_model * 2)
        result = swiglu(input_doubled)
        
        assert result.shape == (self.batch_size, self.d_model)
        
        # Manual computation: Swish(x) * y where x, y are split halves
        a, b = input_doubled.chunk(2, dim=-1)
        expected = swish(a) * b
        
        torch.testing.assert_close(result, expected, rtol=1e-6, atol=1e-6)
    
    def test_geglu_activation(self):
        """Test GeGLU activation function."""
        # GeGLU expects input to be split in half
        input_doubled = torch.randn(self.batch_size, self.d_model * 2)
        result = geglu(input_doubled)
        
        assert result.shape == (self.batch_size, self.d_model)
        
        # Manual computation: GELU(x) * y where x, y are split halves
        a, b = input_doubled.chunk(2, dim=-1)
        expected = gelu(a) * b
        
        torch.testing.assert_close(result, expected, rtol=1e-6, atol=1e-6)
    
    def test_gated_activation_properties(self):
        """Test properties of gated activations."""
        input_doubled = torch.randn(self.batch_size, self.d_model * 2)
        
        gated_functions = [gated_linear_unit, swiglu, geglu]
        
        for gated_fn in gated_functions:
            result = gated_fn(input_doubled)
            
            # Should preserve batch dimension, halve feature dimension
            assert result.shape == (self.batch_size, self.d_model)
            
            # Should be differentiable
            input_doubled_grad = input_doubled.clone().requires_grad_(True)
            output = gated_fn(input_doubled_grad)
            output.sum().backward()
            
            assert input_doubled_grad.grad is not None
            assert torch.isfinite(input_doubled_grad.grad).all()


class TestParametricActivations:
    """Test parametric activation functions."""
    
    def setup_method(self):
        """Set up test fixtures."""
        torch.manual_seed(42)
        self.batch_size = 4
        self.d_model = 64
        self.input_tensor = torch.randn(self.batch_size, self.d_model)
    
    def test_prelu_custom(self):
        """Test custom PReLU implementation."""
        # PReLU parameter
        alpha = 0.2
        result = prelu_custom(self.input_tensor, alpha)
        
        # Manual computation
        expected = torch.where(
            self.input_tensor > 0, 
            self.input_tensor, 
            alpha * self.input_tensor
        )
        
        torch.testing.assert_close(result, expected, rtol=1e-6, atol=1e-6)
    
    def test_prelu_gradient_flow(self):
        """Test gradient flow through PReLU."""
        alpha = 0.1
        x = self.input_tensor.clone().requires_grad_(True)
        y = prelu_custom(x, alpha)
        y.sum().backward()
        
        assert x.grad is not None
        
        # Check gradient values
        expected_grad = torch.where(x > 0, torch.ones_like(x), torch.full_like(x, alpha))
        torch.testing.assert_close(x.grad, expected_grad, rtol=1e-6, atol=1e-6)
    
    def test_elu_plus(self):
        """Test ELU Plus activation."""
        alpha = 1.0
        result = elu_plus(self.input_tensor, alpha)
        
        # ELU Plus should be non-negative
        assert (result >= 0).all()
        
        # For positive inputs, should be x + 1
        positive_mask = self.input_tensor > 0
        if positive_mask.any():
            expected_positive = self.input_tensor[positive_mask] + 1
            torch.testing.assert_close(
                result[positive_mask], expected_positive, rtol=1e-6, atol=1e-6
            )
    
    def test_softmax_temperature(self):
        """Test temperature-scaled softmax."""
        temperatures = [0.1, 1.0, 10.0]
        
        for temp in temperatures:
            result = softmax_temperature(self.input_tensor, temperature=temp)
            
            # Should be valid probability distribution
            assert (result >= 0).all()
            assert torch.allclose(result.sum(dim=-1), torch.ones(self.batch_size))
            
            # Lower temperature should be more peaked
            if temp == 0.1:
                # Should have higher max probability
                max_probs_low = result.max(dim=-1)[0]
            elif temp == 10.0:
                # Should be more uniform
                max_probs_high = result.max(dim=-1)[0]
        
        # Lower temperature should generally produce higher max probabilities
        # (though this isn't guaranteed for all inputs)
        # assert max_probs_low.mean() > max_probs_high.mean()


class TestAdvancedActivations:
    """Test advanced activation functions for specialized use cases."""
    
    def setup_method(self):
        """Set up test fixtures."""
        torch.manual_seed(42)
        self.batch_size = 4
        self.d_model = 64
        self.input_tensor = torch.randn(self.batch_size, self.d_model)
    
    def test_sparsemax(self):
        """Test Sparsemax activation."""
        result = sparsemax(self.input_tensor)
        
        # Should be valid probability distribution
        assert (result >= 0).all()
        assert torch.allclose(result.sum(dim=-1), torch.ones(self.batch_size), atol=1e-5)
        
        # Should be sparser than softmax
        softmax_result = torch.softmax(self.input_tensor, dim=-1)
        
        # Count near-zero elements
        sparsemax_zeros = (result < 1e-6).sum()
        softmax_zeros = (softmax_result < 1e-6).sum()
        
        # Sparsemax should generally be sparser
        assert sparsemax_zeros >= softmax_zeros
    
    def test_gumbel_softmax(self):
        """Test Gumbel Softmax activation."""
        temperatures = [0.1, 1.0, 10.0]
        
        for temp in temperatures:
            result = gumbel_softmax(self.input_tensor, tau=temp)
            
            # Should be valid probability distribution
            assert (result >= 0).all()
            assert torch.allclose(result.sum(dim=-1), torch.ones(self.batch_size), atol=1e-5)
            
            # Should be differentiable
            x = self.input_tensor.clone().requires_grad_(True)
            y = gumbel_softmax(x, tau=temp)
            y.sum().backward()
            
            assert x.grad is not None
            assert torch.isfinite(x.grad).all()
    
    def test_adaptive_activation(self):
        """Test adaptive activation function."""
        # Test with different adaptation parameters
        adaptations = [0.0, 0.5, 1.0]
        
        for adapt in adaptations:
            gate = torch.full_like(self.input_tensor, adapt)
            result = adaptive_activation(self.input_tensor, gate=gate)
            
            assert result.shape == self.input_tensor.shape
            assert torch.isfinite(result).all()
            
            # Different adaptations should produce different results
            if adapt == 0.0:
                result_base = result.clone()
            elif adapt == 1.0:
                result_full = result.clone()
        
        # Different adaptation levels should give different outputs
        assert not torch.allclose(result_base, result_full, rtol=1e-3)


class TestAdaptiveActivationModule:
    """Test the AdaptiveActivation module class."""
    
    def setup_method(self):
        """Set up test fixtures."""
        torch.manual_seed(42)
        self.batch_size = 4
        self.d_model = 64
        self.input_tensor = torch.randn(self.batch_size, self.d_model)
        self.adaptive_activation = AdaptiveActivation(self.d_model)
    
    def test_adaptive_activation_forward(self):
        """Test forward pass of adaptive activation module."""
        result = self.adaptive_activation(self.input_tensor)
        
        assert result.shape == self.input_tensor.shape
        assert torch.isfinite(result).all()
    
    def test_adaptive_activation_parameters(self):
        """Test that adaptive activation has learnable parameters."""
        params = list(self.adaptive_activation.parameters())
        assert len(params) > 0
        
        # Should have parameters for adaptation
        total_params = sum(p.numel() for p in params)
        assert total_params > 0
    
    def test_adaptive_activation_gradient_flow(self):
        """Test gradient flow through adaptive activation."""
        x = self.input_tensor.clone().requires_grad_(True)
        y = self.adaptive_activation(x)
        y.sum().backward()
        
        assert x.grad is not None
        assert torch.isfinite(x.grad).all()
        
        # Module parameters should also receive gradients
        for param in self.adaptive_activation.parameters():
            assert param.grad is not None
            assert torch.isfinite(param.grad).all()
    
    def test_adaptive_activation_training_mode(self):
        """Test behavior in training vs evaluation mode."""
        # Training mode
        self.adaptive_activation.train()
        result_train = self.adaptive_activation(self.input_tensor)
        
        # Evaluation mode
        self.adaptive_activation.eval()
        result_eval = self.adaptive_activation(self.input_tensor)
        
        # Results might be different due to adaptive behavior
        assert result_train.shape == result_eval.shape
        assert torch.isfinite(result_train).all()
        assert torch.isfinite(result_eval).all()


class TestMixtureOfActivations:
    """Test Mixture of Activations module."""
    
    def setup_method(self):
        """Set up test fixtures."""
        torch.manual_seed(42)
        self.batch_size = 4
        self.d_model = 64
        self.num_activations = 3
        self.input_tensor = torch.randn(self.batch_size, self.d_model)
        
        self.mixture = MixtureOfActivations(
            d_model=self.d_model,
            activations=["gelu", "swish", "relu"]
        )
    
    def test_mixture_forward_pass(self):
        """Test forward pass of mixture of activations."""
        result = self.mixture(self.input_tensor)
        
        assert result.shape == self.input_tensor.shape
        assert torch.isfinite(result).all()
    
    def test_mixture_gating_weights(self):
        """Test that gating weights are valid probabilities."""
        # Access internal gating weights if available
        with torch.no_grad():
            # This would depend on the exact implementation
            result = self.mixture(self.input_tensor)
            
            # Basic check - output should be reasonable
            assert torch.isfinite(result).all()
    
    def test_mixture_gradient_flow(self):
        """Test gradient flow through mixture of activations."""
        x = self.input_tensor.clone().requires_grad_(True)
        y = self.mixture(x)
        y.sum().backward()
        
        assert x.grad is not None
        assert torch.isfinite(x.grad).all()
        
        # Module parameters should receive gradients
        for param in self.mixture.parameters():
            assert param.grad is not None
            assert torch.isfinite(param.grad).all()
    
    def test_mixture_specialization(self):
        """Test that different activations in mixture can specialize."""
        # Train on different types of data to encourage specialization
        optimizer = torch.optim.Adam(self.mixture.parameters(), lr=0.01)
        
        # Positive data
        positive_data = torch.abs(torch.randn(self.batch_size, self.d_model))
        
        # Train a few steps
        for _ in range(5):
            optimizer.zero_grad()
            output = self.mixture(positive_data)
            loss = -output.mean()  # Maximize positive outputs
            loss.backward()
            optimizer.step()
        
        # Test that mixture still works
        result = self.mixture(self.input_tensor)
        assert result.shape == self.input_tensor.shape
        assert torch.isfinite(result).all()


class TestActivationUtilities:
    """Test activation utility functions."""
    
    def setup_method(self):
        """Set up test fixtures."""
        torch.manual_seed(42)
        self.batch_size = 4
        self.d_model = 64
        self.input_tensor = torch.randn(self.batch_size, self.d_model)
    
    def test_get_activation_fn(self):
        """Test activation function factory."""
        activation_names = ['relu', 'gelu', 'swish', 'mish', 'tanh', 'sigmoid']
        
        for name in activation_names:
            activation_fn = get_activation_fn(name)
            
            # Should return a callable
            assert callable(activation_fn)
            
            # Should work with our test input
            result = activation_fn(self.input_tensor)
            assert result.shape == self.input_tensor.shape
            assert torch.isfinite(result).all()
    
    def test_activation_fn_consistency(self):
        """Test that activation functions are consistent with PyTorch implementations."""
        # Test GELU
        our_gelu = get_activation_fn('gelu')
        torch_gelu = torch.nn.functional.gelu
        
        our_result = our_gelu(self.input_tensor)
        torch_result = torch_gelu(self.input_tensor)
        
        torch.testing.assert_close(our_result, torch_result, rtol=1e-4, atol=1e-5)
        
        # Test ReLU
        our_relu = get_activation_fn('relu')
        torch_relu = torch.nn.functional.relu
        
        our_result = our_relu(self.input_tensor)
        torch_result = torch_relu(self.input_tensor)
        
        torch.testing.assert_close(our_result, torch_result, rtol=1e-6, atol=1e-6)
    
    def test_unknown_activation_handling(self):
        """Test handling of unknown activation names."""
        with pytest.raises((ValueError, KeyError)):
            get_activation_fn('unknown_activation')


class TestActivationIntegration:
    """Integration tests for activations in DELTA context."""
    
    def setup_method(self):
        """Set up test fixtures for integration testing."""
        torch.manual_seed(42)
        self.batch_size = 4
        self.d_model = 128
        self.input_tensor = torch.randn(self.batch_size, self.d_model)
    
    def test_activation_chain(self):
        """Test chaining multiple activations as might occur in DELTA blocks."""
        # Simulate processing through multiple stages
        x = self.input_tensor
        
        # Stage 1: Thesis generation (linear + activation)
        x1 = gelu(x)
        
        # Stage 2: Attractor processing (different activation)
        x2 = swish(x1)
        
        # Stage 3: Synthesis result processing
        x3 = mish(x2)
        
        # Stage 4: Final gating
        gate_input = torch.cat([x3, x], dim=-1)  # Concatenate for gating
        x4 = swiglu(gate_input)
        
        # Final result should be half the concatenated dimension
        assert x4.shape == (self.batch_size, self.d_model)
        assert torch.isfinite(x4).all()
    
    def test_gradient_flow_through_activation_chain(self):
        """Test gradient flow through a chain of activations."""
        x = self.input_tensor.clone().requires_grad_(True)
        
        # Chain of activations
        x1 = gelu(x)
        x2 = swish(x1)
        x3 = mish(x2)
        
        loss = x3.sum()
        loss.backward()
        
        assert x.grad is not None
        assert torch.isfinite(x.grad).all()
        assert not torch.allclose(x.grad, torch.zeros_like(x.grad))
    
    def test_activation_stability_under_extreme_inputs(self):
        """Test activation stability with extreme inputs that might occur in DELTA."""
        extreme_inputs = [
            torch.full((self.batch_size, self.d_model), 50.0),   # Very large positive
            torch.full((self.batch_size, self.d_model), -50.0),  # Very large negative
            torch.full((self.batch_size, self.d_model), 1e-10),  # Very small positive
            torch.full((self.batch_size, self.d_model), -1e-10), # Very small negative
        ]
        
        activations = [gelu, swish, mish, smooth_relu, hard_swish]
        
        for activation in activations:
            for extreme_input in extreme_inputs:
                result = activation(extreme_input)
                
                # Should not produce NaN or infinite values
                assert torch.isfinite(result).all()
                assert not torch.isnan(result).any()
    
    def test_activation_performance_consistency(self):
        """Test that activations produce consistent results across multiple calls."""
        # Multiple calls with same input should give same result
        activations = [gelu, swish, mish]
        
        for activation in activations:
            result1 = activation(self.input_tensor)
            result2 = activation(self.input_tensor)
            
            torch.testing.assert_close(result1, result2, rtol=1e-10, atol=1e-10)
    
    def test_batch_size_invariance(self):
        """Test that activations work correctly with different batch sizes."""
        batch_sizes = [1, 2, 8, 16]
        
        for batch_size in batch_sizes:
            input_tensor = torch.randn(batch_size, self.d_model)
            
            for activation in [gelu, swish, mish]:
                result = activation(input_tensor)
                
                assert result.shape == input_tensor.shape
                assert torch.isfinite(result).all()


if __name__ == "__main__":
    pytest.main([__file__])
