"""Feed-Forward Network components for DELTA architecture.

This module provides reusable FFN components that can be used across
different parts of the DELTA architecture, eliminating code duplication.
"""

import torch
import torch.nn as nn
from torch import Tensor
from typing import Optional, Union, Callable

from ..math.activations import get_activation_fn
from ..core.base.abstract_implementations import AbstractFeedForwardNetwork


class FeedForwardNetwork(AbstractFeedForwardNetwork):
    """Standard two-layer feed-forward network with configurable activation.
    
    This is the canonical FFN implementation used in transformer architectures
    and DELTA blocks. It follows the pattern:
    
    FFN(x) = W2 * activation(W1 * x + b1) + b2
    
    Args:
        d_model: Input/output dimension
        d_ff: Hidden dimension (defaults to 4 * d_model)
        activation: Activation function name or callable
        dropout: Dropout probability
        bias: Whether to use bias in linear layers
    """
    
    def __init__(
        self,
        d_model: int,
        d_ff: Optional[int] = None,
        activation: Union[str, Callable] = "gelu",
        dropout: float = 0.1,
        bias: bool = True
    ):
        super().__init__(d_model, d_ff)
        
        # Get activation function
        if isinstance(activation, str):
            self.activation = get_activation_fn(activation)
        else:
            self.activation = activation
            
        # Linear layers
        self.linear1 = nn.Linear(d_model, self.d_ff, bias=bias)
        self.dropout = nn.Dropout(dropout)
        self.linear2 = nn.Linear(self.d_ff, d_model, bias=bias)
        
        # Initialize weights
        self._initialize_weights()
    
    def _initialize_weights(self):
        """Initialize weights using Xavier uniform initialization."""
        nn.init.xavier_uniform_(self.linear1.weight)
        nn.init.xavier_uniform_(self.linear2.weight)
        
        if self.linear1.bias is not None:
            nn.init.zeros_(self.linear1.bias)
        if self.linear2.bias is not None:
            nn.init.zeros_(self.linear2.bias)
    
    def forward(self, x: Tensor) -> Tensor:
        """Apply feed-forward transformation.
        
        Args:
            x: Input tensor of shape (..., d_model)
            
        Returns:
            Output tensor of same shape after FFN transformation
        """
        # First linear layer + activation
        hidden = self.activation(self.linear1(x))
        
        # Dropout
        hidden = self.dropout(hidden)
        
        # Second linear layer
        output = self.linear2(hidden)
        
        return output


class GLUFeedForwardNetwork(AbstractFeedForwardNetwork):
    """SwiGLU (Swish-Gated Linear Unit) variant of feed-forward network.
    
    This is a widely used and effective FFN variant.
    SwiGLU(x, W, V, W2) = (Swish(xW) * xV) @ W2
    where Swish(x) = x * sigmoid(x).
    
    Args:
        d_model: Input/output dimension
        d_ff: Hidden dimension. If None, defaults to a value computed
              to keep parameter count similar to a standard FFN,
              typically (2/3) * 4 * d_model.
        dropout: Dropout probability
        bias: Whether to use bias in linear layers
    """
    
    def __init__(
        self,
        d_model: int,
        d_ff: Optional[int] = None, # activation is hardcoded to swish
        dropout: float = 0.1,
        bias: bool = True
    ):
        # SwiGLU typically uses a different hidden dim factor
        if d_ff is None:
            d_ff = int(d_model * 4 * 2 / 3)
            # Ensure it's divisible by a common multiple for efficiency
            d_ff = (d_ff + 255) & -256

        super().__init__(d_model, d_ff)
        
        self.w1 = nn.Linear(d_model, self.d_ff, bias=bias) # Gated path
        self.w3 = nn.Linear(d_model, self.d_ff, bias=bias) # Value path
        self.dropout = nn.Dropout(dropout)
        self.w2 = nn.Linear(self.d_ff, d_model, bias=bias) # Output projection
        
        # Initialize weights
        self._initialize_weights()
    
    def _initialize_weights(self):
        """Initialize weights using Xavier uniform initialization."""
        for layer in [self.w1, self.w2, self.w3]:
            nn.init.xavier_uniform_(layer.weight)
            if layer.bias is not None:
                nn.init.zeros_(layer.bias)
    
    def forward(self, x: Tensor) -> Tensor:
        """Apply GLU feed-forward transformation.
        
        Args:
            x: Input tensor of shape (..., d_model)
            
        Returns:
            Output tensor of same shape after GLU FFN transformation
        """
        # Apply Swish to the gated path and multiply with the value path
        gated = nn.functional.silu(self.w1(x)) * self.w3(x)
        
        # Dropout
        gated_dropout = self.dropout(gated)
        output = self.w2(gated_dropout)
        
        return output


# Convenience function for creating FFN instances
def create_ffn(
    d_model: int,
    d_ff: Optional[int] = None,
    ffn_type: str = "standard",
    activation: Union[str, Callable] = "gelu",
    dropout: float = 0.1,
    bias: bool = True
) -> AbstractFeedForwardNetwork:
    """Create a feed-forward network instance.
    
    Args:
        d_model: Input/output dimension
        d_ff: Hidden dimension
        ffn_type: Type of FFN ("standard" or "glu")
        activation: Activation function
        dropout: Dropout probability
        bias: Whether to use bias
        
    Returns:
        FFN instance
    """
    if ffn_type == "standard":
        return FeedForwardNetwork(d_model, d_ff, activation, dropout, bias)
    elif ffn_type == "glu":
        # GLU activation is typically hardcoded (e.g., sigmoid, swish)
        # The provided activation is ignored for GLU to use the modern SwiGLU.
        # If other GLU variants are needed, this logic can be expanded.
        return GLUFeedForwardNetwork(d_model, d_ff, dropout, bias)
    else:
        raise ValueError(f"Unknown FFN type: {ffn_type}")
