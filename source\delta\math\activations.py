"""Activation functions for DELTA architecture.

This module provides activation functions used throughout the DELTA model,
including standard and custom activations.
"""

import torch
from torch import Tensor
import torch.nn.functional as F
from typing import Optional
import math

from ..utils.constants import EPSILON


def gelu(x: Tensor) -> Tensor:
    """Gaussian Error Linear Unit activation.
    
    GELU(x) = x * Φ(x) where Φ is the cumulative distribution function
    of the standard normal distribution.
    
    Args:
        x: Input tensor
        
    Returns:
        Activated tensor
    """
    return F.gelu(x)


def swish(x: Tensor, beta: float = 1.0) -> Tensor:
    """Swish activation function.
    
    Swish(x) = x * sigmoid(β * x)
    
    Args:
        x: Input tensor
        beta: Scaling parameter
        
    Returns:
        Activated tensor
    """
    return x * torch.sigmoid(beta * x)


def mish(x: Tensor) -> Tensor:
    """Mish activation function.
    
    Mish(x) = x * tanh(softplus(x))
    
    Args:
        x: Input tensor
        
    Returns:
        Activated tensor
    """
    return x * torch.tanh(F.softplus(x))


def gated_linear_unit(x: Tensor, dim: int = -1) -> Tensor:
    """Gated Linear Unit activation.
    
    GLU(x) = x[:d/2] * sigmoid(x[d/2:])
    
    Args:
        x: Input tensor
        dim: Dimension to split along
        
    Returns:
        Activated tensor with half the size along dim
    """
    x1, x2 = x.chunk(2, dim=dim)
    return x1 * torch.sigmoid(x2)


def swiglu(x: Tensor, dim: int = -1) -> Tensor:
    """SwiGLU activation (Swish-Gated Linear Unit).
    
    SwiGLU(x) = Swish(x[:d/2]) * x[d/2:]
    
    Args:
        x: Input tensor
        dim: Dimension to split along
        
    Returns:
        Activated tensor with half the size along dim
    """
    x1, x2 = x.chunk(2, dim=dim)
    return swish(x1) * x2


def geglu(x: Tensor, dim: int = -1) -> Tensor:
    """GeGLU activation (GELU-Gated Linear Unit).
    
    GeGLU(x) = GELU(x[:d/2]) * x[d/2:]
    
    Args:
        x: Input tensor
        dim: Dimension to split along
        
    Returns:
        Activated tensor with half the size along dim
    """
    x1, x2 = x.chunk(2, dim=dim)
    return gelu(x1) * x2


def adaptive_activation(
    x: Tensor,
    gate: Tensor,
    activation_fn: str = "gelu"
) -> Tensor:
    """Adaptive activation with learned gating.
    
    Args:
        x: Input tensor
        gate: Gating tensor (same shape as x)
        activation_fn: Base activation function
        
    Returns:
        Adaptively activated tensor
    """
    # Apply base activation
    if activation_fn == "gelu":
        activated = gelu(x)
    elif activation_fn == "relu":
        activated = F.relu(x)
    elif activation_fn == "swish":
        activated = swish(x)
    else:
        raise ValueError(f"Unknown activation: {activation_fn}")
    
    # Apply adaptive gating
    gate_sigmoid = torch.sigmoid(gate)
    return gate_sigmoid * activated + (1 - gate_sigmoid) * x


def smooth_relu(x: Tensor, beta: float = 1.0) -> Tensor:
    """Smooth approximation of ReLU.

    SmoothReLU(x) = (1/β) * log(1 + exp(β * x))

    This is equivalent to (1/β) * softplus(β * x), using the numerically
    stable softplus implementation to avoid overflow.

    Args:
        x: Input tensor
        beta: Smoothness parameter

    Returns:
        Activated tensor
    """
    return (1.0 / beta) * F.softplus(beta * x)


def hard_swish(x: Tensor) -> Tensor:
    """Hard Swish activation (more efficient approximation).
    
    HardSwish(x) = x * ReLU6(x + 3) / 6
    
    Args:
        x: Input tensor
        
    Returns:
        Activated tensor
    """
    return x * F.relu6(x + 3) / 6


def hard_sigmoid(x: Tensor) -> Tensor:
    """Hard Sigmoid activation (more efficient approximation).
    
    HardSigmoid(x) = ReLU6(x + 3) / 6
    
    Args:
        x: Input tensor
        
    Returns:
        Activated tensor
    """
    return F.relu6(x + 3) / 6


def prelu_custom(x: Tensor, alpha: float = 0.25) -> Tensor:
    """Parametric ReLU with custom alpha.
    
    PReLU(x) = max(0, x) + alpha * min(0, x)
    
    Args:
        x: Input tensor
        alpha: Negative slope parameter
        
    Returns:
        Activated tensor
    """
    return F.leaky_relu(x, negative_slope=alpha)


def elu_plus(x: Tensor, alpha: float = 1.0, beta: float = 1.0) -> Tensor:
    """ELU activation shifted to be non-negative.

    ELU+(x) = beta * (ELU(x, alpha) + 1)

    This ensures the output is always non-negative by adding 1 to the ELU output.
    For positive inputs: ELU+(x) = beta * (x + 1)
    For negative inputs: ELU+(x) = beta * (alpha * (exp(x) - 1) + 1)

    Args:
        x: Input tensor
        alpha: ELU alpha parameter
        beta: Output scaling parameter

    Returns:
        Activated tensor (always non-negative)
    """
    return beta * (F.elu(x, alpha=alpha) + 1.0)


def softmax_temperature(
    x: Tensor,
    temperature: float = 1.0,
    dim: int = -1
) -> Tensor:
    """Softmax with temperature scaling.
    
    Args:
        x: Input tensor
        temperature: Temperature parameter
        dim: Dimension to apply softmax
        
    Returns:
        Softmax output
    """
    return F.softmax(x / temperature, dim=dim)


def sparsemax(x: Tensor, dim: int = -1) -> Tensor:
    """Sparsemax activation (sparse alternative to softmax).
    
    Projects onto the simplex with potential sparsity.
    
    Args:
        x: Input tensor
        dim: Dimension to apply sparsemax
        
    Returns:
        Sparsemax output
    """
    # Sort in descending order
    sorted_x, _ = torch.sort(x, dim=dim, descending=True)
    
    # Compute cumulative sum
    cumsum = torch.cumsum(sorted_x, dim=dim)
    
    # Find threshold
    k = torch.arange(1, x.size(dim) + 1, device=x.device).float()
    threshold = (cumsum - 1) / k
    
    # Find support
    support = sorted_x > threshold
    k_max = support.sum(dim=dim, keepdim=True).float()
    
    # Compute threshold
    tau = (cumsum.gather(dim, k_max.long() - 1) - 1) / k_max
    
    # Apply sparsemax
    return torch.clamp(x - tau, min=0)


def gumbel_softmax(
    logits: Tensor,
    tau: float = 1.0,
    hard: bool = False,
    dim: int = -1
) -> Tensor:
    """Gumbel-Softmax activation for differentiable sampling.
    
    Args:
        logits: Input logits
        tau: Temperature parameter
        hard: Whether to use straight-through estimator
        dim: Dimension to apply softmax
        
    Returns:
        Gumbel-softmax output
    """
    return F.gumbel_softmax(logits, tau=tau, hard=hard, dim=dim)


class AdaptiveActivation(torch.nn.Module):
    """Learnable adaptive activation module."""
    
    def __init__(self, d_model: int, activation_fn: str = "gelu"):
        super().__init__()
        self.d_model = d_model
        self.activation_fn = activation_fn
        
        # Learnable gating parameters
        self.gate_weight = torch.nn.Parameter(torch.zeros(d_model))
        self.gate_bias = torch.nn.Parameter(torch.zeros(d_model))
        
    def forward(self, x: Tensor) -> Tensor:
        """Apply adaptive activation.
        
        Args:
            x: Input tensor
            
        Returns:
            Adaptively activated tensor
        """
        gate = x * self.gate_weight + self.gate_bias
        return adaptive_activation(x, gate, self.activation_fn)


class MixtureOfActivations(torch.nn.Module):
    """Mixture of different activation functions."""
    
    def __init__(
        self,
        d_model: int,
        activations: list[str] = ["gelu", "swish", "relu"]
    ):
        super().__init__()
        self.d_model = d_model
        self.activations = activations
        self.n_activations = len(activations)
        
        # Mixing weights
        self.mixing_weights = torch.nn.Parameter(
            torch.ones(self.n_activations) / self.n_activations
        )
        
    def forward(self, x: Tensor) -> Tensor:
        """Apply mixture of activations.
        
        Args:
            x: Input tensor
            
        Returns:
            Mixed activation output
        """
        weights = F.softmax(self.mixing_weights, dim=0)
        output = torch.zeros_like(x)
        
        for i, act_name in enumerate(self.activations):
            if act_name == "gelu":
                act_output = gelu(x)
            elif act_name == "swish":
                act_output = swish(x)
            elif act_name == "relu":
                act_output = F.relu(x)
            elif act_name == "mish":
                act_output = mish(x)
            else:
                raise ValueError(f"Unknown activation: {act_name}")
            
            output = output + weights[i] * act_output
        
        return output


def get_activation_fn(name: str):
    """Get activation function by name.
    
    Args:
        name: Activation function name
        
    Returns:
        Activation function
    """
    activations = {
        "relu": F.relu,
        "gelu": gelu,
        "swish": swish,
        "mish": mish,
        "tanh": torch.tanh,
        "sigmoid": torch.sigmoid,
        "hard_swish": hard_swish,
        "hard_sigmoid": hard_sigmoid,
        "elu": F.elu,
        "leaky_relu": F.leaky_relu,
        "selu": F.selu,
        "softplus": F.softplus,
    }
    
    if name not in activations:
        raise ValueError(f"Unknown activation: {name}")
    
    return activations[name]
