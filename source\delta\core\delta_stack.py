"""DELTA Stack Implementation

This module implements the DELTA stack, which is a sequence of DELTA blocks
that processes input states through the entire network architecture.
"""

import torch
import torch.nn as nn
from typing import List, Optional, Tuple
from dataclasses import dataclass

from ..base.types import DELTAConfig, KappaCache
from ..base.abstract_implementations import Abstract<PERSON><PERSON>AStack
from .delta_block import DE<PERSON><PERSON><PERSON>

@dataclass
class DELTAStackOutput:
    h_out: torch.Tensor
    h_sem_out: List[Optional[List[torch.Tensor]]]
    all_local_antitheses: List[torch.Tensor]

class DELTAStack(AbstractDELTAStack):
    """
    A stack of DELTA blocks that processes input through the network.
    
    During prefill, this populates the κ-Cache with local antitheses at each timestep.
    During decoding, this uses the κ-Cache for context attention.
    """

    def __init__(self, config: DELTAConfig):
        super().__init__(config)
        self.num_layers = config.n_layers
        self.config = config
        
        # Create a list of DELTA blocks
        self.layers = nn.ModuleList([
            DELTABlock(config) for _ in range(self.num_layers)
        ])

    def forward(
        self,
        h_in: torch.Tensor,
        h_sem_in: List[Optional[List[torch.Tensor]]],
        kappa_cache: Optional[KappaCache] = None
    ) -> DELTAStackOutput:
        """
        Performs a forward pass through the entire stack of DELTA blocks.
        
        This method handles both prefill and decoding phases:
        - Prefill phase: When kappa_cache=None, processes without cache attention
        - Decoding phase: When kappa_cache is provided, uses cache for context attention

        Args:
            h_in: The input state for the first block in the stack.
                  Shape: (batch_size, d_model).
            h_sem_in: A list of lists of recurrent semantic hidden states.
                      Outer list has length `num_layers`, inner lists contain
                      states for each attractor in that layer.
            kappa_cache: The kappa-cache used during the decoding phase.
                        None during prefill phase.

        Returns:
            A DELTAStackOutput tuple containing:
            - h_out: The final output state from the last block.
            - h_sem_out: The list of updated semantic hidden states for all layers.
            - all_local_antitheses: A list of the local antithesis tensors
                                    generated by each layer.
        """
        assert len(h_sem_in) == self.num_layers, \
            "Must provide a list of semantic states for each layer."

        h_intermediate = h_in
        h_sem_out_all_layers: List[Optional[List[torch.Tensor]]] = []
        all_local_antitheses: List[torch.Tensor] = []

        for i, layer in enumerate(self.layers):
            block_output = layer(
                h_in=h_intermediate,
                h_sem_in=h_sem_in[i],
                kappa_cache=kappa_cache,
                layer_idx=i
            )

            # The output state of this layer is the input to the next
            h_intermediate = block_output.h_out

            # Collect the outputs
            h_sem_out_all_layers.append(block_output.semantic_states)
            all_local_antitheses.append(block_output.local_antithesis)

        return DELTAStackOutput(
            h_out=h_intermediate,
            h_sem_out=h_sem_out_all_layers,
            all_local_antitheses=all_local_antitheses
        )



    def get_local_loss_parameters(self) -> List[nn.Parameter]:
        """Collect all parameters used for local (thesis) loss."""
        params = []
        for layer in self.layers:
            params.extend(layer.get_local_loss_parameters())
        return params

    def get_global_loss_parameters(self) -> List[nn.Parameter]:
        """Collect all parameters used for global (antithesis/control) loss."""
        params = []
        for layer in self.layers:
            params.extend(layer.get_global_loss_parameters())
        return params
