import torch
import torch.nn as nn
from typing import List, Optional, Dict, Union
from pathlib import Path

class KappaCache(nn.Module):
    """
    A container for the layer-specific κ-Caches as defined in the DELTA specification.

    This cache is populated once during the Prefill Phase and is treated as a read-only
    persistent memory store during the autoregressive Decoding Phase. It stores the
    Antithesis (κ) vectors generated from the input prompt.

    Args:
        num_layers (int): The number of DELTA layers in the model.
        d_model (int): The dimensionality of the model.
        max_seq_length (int): Maximum sequence length to support.
        dtype (torch.dtype): Data type for cache tensors.
        pin_memory (bool): Whether to pin memory for faster GPU transfers.
    """
    def __init__(self, num_layers: int, d_model: int, max_seq_length: int = 2048, 
                 dtype: torch.dtype = torch.float32, pin_memory: bool = False):
        super().__init__()
        self.num_layers = num_layers
        self.d_model = d_model
        self.max_seq_length = max_seq_length
        self.dtype = dtype
        self.pin_memory = pin_memory
        
        # Current state
        self.seq_length = 0
        self.batch_size = 0
        self.device = torch.device("cpu")
        self._is_populated = False
        
        # The cache is a dict mapping layer_idx -> tensor of shape (batch_size, seq_len, d_model)
        # We use a standard Python dict and handle device placement manually
        self.register_buffer('_initialized', torch.tensor(False))
        self.cache: Dict[int, torch.Tensor] = {}

    def initialize(self, batch_size: int, seq_length: int, device: torch.device):
        """
        Allocates memory for the cache at the beginning of the Prefill Phase.

        Args:
            batch_size (int): The batch size of the input.
            seq_length (int): The length of the input prompt.
            device (torch.device): The device to store the cache on (e.g., 'cuda').
        """
        if seq_length > self.max_seq_length:
            raise ValueError(f"Sequence length {seq_length} exceeds max_seq_length {self.max_seq_length}")
        
        self.batch_size = batch_size
        self.seq_length = seq_length
        self.device = device
        
        # Clear any existing cache
        self.cache.clear()
        
        # Allocate cache tensors for each layer
        for layer_idx in range(self.num_layers):
            cache_tensor = torch.zeros(
                batch_size, seq_length, self.d_model, 
                device=device, dtype=self.dtype
            )
            
            # Pin memory if requested and on CPU
            if self.pin_memory and device.type == 'cpu':
                cache_tensor = cache_tensor.pin_memory()
                
            self.cache[layer_idx] = cache_tensor
        
        self._initialized.fill_(True)
        self._is_populated = False

    def set_vector_at_position(self, layer_idx: int, position: int, kappa_vector: torch.Tensor):
        """
        Sets the κ vector for a specific token position within a specific layer's cache.
        This is called sequentially during the Prefill Phase.

        Args:
            layer_idx (int): The index of the layer to update.
            position (int): The token position in the prompt sequence.
            kappa_vector (torch.Tensor): The κ vector to store. Shape: (batch_size, d_model).
        """
        if not self._initialized:
            raise RuntimeError("Cache must be initialized before setting vectors.")
        
        if layer_idx not in self.cache:
            raise ValueError(f"Layer {layer_idx} not found in cache")
        
        if position >= self.seq_length:
            raise ValueError(f"Position {position} exceeds sequence length {self.seq_length}")
        
        if kappa_vector.shape != (self.batch_size, self.d_model):
            raise ValueError(f"Expected shape ({self.batch_size}, {self.d_model}), got {kappa_vector.shape}")
        
        self.cache[layer_idx][:, position, :] = kappa_vector

    def set_layer_cache(self, layer_idx: int, kappa_vectors: torch.Tensor):
        """
        Sets the entire κ-Cache for a specific layer.

        Args:
            layer_idx (int): The index of the layer to update.
            kappa_vectors (torch.Tensor): Shape: (batch_size, seq_length, d_model).
        """
        if not self._initialized:
            raise RuntimeError("Cache must be initialized before setting vectors.")
        
        expected_shape = (self.batch_size, self.seq_length, self.d_model)
        if kappa_vectors.shape != expected_shape:
            raise ValueError(f"Expected shape {expected_shape}, got {kappa_vectors.shape}")
        
        self.cache[layer_idx] = kappa_vectors.to(device=self.device, dtype=self.dtype)

    def get_cache_for_layer(self, layer_idx: int) -> torch.Tensor:
        """
        Retrieves the full κ-Cache for a specific layer.
        This is called during the Decoding Phase.

        Args:
            layer_idx (int): The index of the layer whose cache is to be retrieved.

        Returns:
            torch.Tensor: The κ-Cache for the specified layer.
                          Shape: (batch_size, seq_length, d_model).
        """
        if layer_idx not in self.cache:
            raise RuntimeError(f"Cache for layer {layer_idx} has not been populated.")
        return self.cache[layer_idx]

    def is_populated(self) -> bool:
        """Checks if the cache has been initialized and populated."""
        return self._initialized.item() and len(self.cache) == self.num_layers

    @property
    def is_empty(self) -> bool:
        """Returns True if cache is empty."""
        return not self.is_populated()

    def clear(self):
        """Clear the cache and reset all state."""
        self.cache.clear()
        self.seq_length = 0
        self.batch_size = 0
        self._is_populated = False
        self._initialized.fill_(False)

    def to_device(self, device: torch.device) -> 'KappaCache':
        """Move cache to specified device."""
        self.device = device
        for layer_idx in self.cache:
            self.cache[layer_idx] = self.cache[layer_idx].to(device)
        return self

    def state_dict_custom(self) -> Dict:
        """Custom state dict that includes cache contents."""
        state = {
            'num_layers': self.num_layers,
            'd_model': self.d_model,
            'max_seq_length': self.max_seq_length,
            'seq_length': self.seq_length,
            'batch_size': self.batch_size,
            'dtype': str(self.dtype),
            'cache': {k: v.cpu() for k, v in self.cache.items()},
            'is_populated': self._is_populated
        }
        return state

    def load_state_dict_custom(self, state_dict: Dict):
        """Load from custom state dict."""
        self.num_layers = state_dict['num_layers']
        self.d_model = state_dict['d_model']
        self.max_seq_length = state_dict['max_seq_length']
        self.seq_length = state_dict['seq_length']
        self.batch_size = state_dict['batch_size']
        self.dtype = getattr(torch, state_dict['dtype'].split('.')[-1])
        self._is_populated = state_dict['is_populated']
        
        self.cache.clear()
        for k, v in state_dict['cache'].items():
            self.cache[int(k)] = v.to(device=self.device, dtype=self.dtype)
        
        self._initialized.fill_(True)

    def save(self, filepath: Union[str, Path]):
        """Save cache to disk."""
        torch.save(self.state_dict_custom(), filepath)

    @classmethod
    def load(cls, filepath: Union[str, Path], device: torch.device = None) -> 'KappaCache':
        """Load cache from disk."""
        state_dict = torch.load(filepath, map_location='cpu')
        
        cache = cls(
            num_layers=state_dict['num_layers'],
            d_model=state_dict['d_model'],
            max_seq_length=state_dict['max_seq_length'],
            dtype=getattr(torch, state_dict['dtype'].split('.')[-1])
        )
        
        if device is not None:
            cache.device = device
        
        cache.load_state_dict_custom(state_dict)
        return cache
        
    def get_memory_usage(self) -> Dict[str, Union[int, float]]:
        """Get memory usage statistics for the cache."""
        if not self._initialized:
            return {"total_bytes": 0, "total_mb": 0.0, "num_tensors": 0}
            
        total_bytes = 0
        for tensor in self.cache.values():
            total_bytes += tensor.element_size() * tensor.numel()
            
        return {
            "total_bytes": total_bytes,
            "total_mb": total_bytes / (1024 * 1024),
            "num_tensors": len(self.cache),
            "shape_per_layer": f"({self.batch_size}, {self.seq_length}, {self.d_model})"
        }

    def __repr__(self) -> str:
        return (f"KappaCache(num_layers={self.num_layers}, d_model={self.d_model}, "
                f"seq_length={self.seq_length}, batch_size={self.batch_size}, "
                f"populated={self.is_populated()}, device={self.device})")
