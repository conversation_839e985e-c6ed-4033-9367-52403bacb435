"""A low-rank operator component for the DELTA architecture."""

import torch
import torch.nn as nn
from torch import Tensor


class LowRankOperator(nn.Module):
    """
    Implements an efficient, low-rank linear transformation.

    This component models a full-rank d x d matrix operator as the
    product of two smaller, low-rank matrices: F = AB^T. This is
    vastly more parameter-efficient than a standard nn.Linear layer
    for large d.

    The transformation applied is: y = (AB^T)x

    Args:
        d_model (int): The input and output dimension of the operator.
        rank (int): The rank of the factorization. A smaller rank means
                    fewer parameters and a more constrained transformation.
        bias (bool): If True, adds a learnable bias to the output.
                     Defaults to False.
    """

    def __init__(self, d_model: int, rank: int, bias: bool = False):
        super().__init__()
        if not (d_model > 0 and rank > 0):
            raise ValueError("d_model and rank must be positive integers.")

        self.d_model = d_model
        self.rank = rank

        # The two low-rank matrices that define the operator
        self.A = nn.Parameter(torch.empty(d_model, rank))
        self.B = nn.Parameter(torch.empty(d_model, rank))

        if bias:
            self.bias = nn.Parameter(torch.empty(d_model))
        else:
            self.register_parameter("bias", None)

        self._initialize_weights()

    def _initialize_weights(self):
        """Initializes weights using Xavier uniform initialization."""
        nn.init.xavier_uniform_(self.A)
        nn.init.xavier_uniform_(self.B)
        if self.bias is not None:
            nn.init.zeros_(self.bias)

    def forward(self, x: Tensor) -> Tensor:
        """Applies the learned low-rank transformation to the input tensor."""
        # Efficient computation of (A @ B.T) @ x is x @ (A @ B.T).T = x @ B @ A.T
        projected = torch.matmul(x, self.B)
        output = torch.matmul(projected, self.A.T)

        if self.bias is not None:
            output = output + self.bias

        return output

    def __repr__(self):
        return (
            f"{self.__class__.__name__}(d_model={self.d_model}, "
            f"rank={self.rank}, bias={self.bias is not None})"
        )

