"""Gating Networks for the DELTA Antithesis Generation.

This module implements the gating networks that control the weighting of
structural and semantic attractors in the antithesis generation process,
as defined in DeltaDecoder.md Section 3.1.2.
"""

import torch
import torch.nn as nn
from torch import Tensor
from typing import Optional

from ..base.abstract_implementations import AbstractGatingNetwork
from ...utils.constants import DEFAULT_N_ATTRACTORS, EPSILON


class StructuralGatingNetwork(AbstractGatingNetwork):
    """Gating network for structural attractors.
    
    Per DeltaDecoder.md Section 3.1.2, computes softmax-normalized weights
    for structural attractors based on the summary vector:
    g_Struct = Softmax(W_gStruct * v_summary + b_gStruct)
    
    Args:
        d_model: Model dimension
        num_attractors: Number of structural attractors to gate
        dropout: Dropout probability for regularization
    """
    
    def __init__(
        self,
        d_model: int,
        num_attractors: int = DEFAULT_N_ATTRACTORS,
        dropout: float = 0.0
    ):
        super().__init__(d_model, num_attractors)
        
        # Gating projection: W_gStruct and b_gStruct from spec
        self.gate_projection = nn.Linear(d_model, num_attractors, bias=True)
        # Optional dropout for regularization (disabled by default for spec compliance)
        self.dropout = nn.Dropout(dropout) if dropout > 0.0 else nn.Identity()
        
        # Initialize weights for balanced initial gating
        self._initialize_weights()
    
    def _initialize_weights(self) -> None:
        """Initialize gating weights for balanced attractor selection."""
        # Use small random weights to encourage balanced initial selection
        nn.init.normal_(self.gate_projection.weight, mean=0.0, std=0.02)
        nn.init.zeros_(self.gate_projection.bias)
    
    def forward(self, summary_vector: Tensor) -> Tensor:
        """Compute structural attractor gating weights.
        
        Args:
            summary_vector: Summary of the thesis (v_summary)
            
        Returns:
            Softmax-normalized gating weights of shape (batch_size, num_attractors)
        """
        # Apply dropout for regularization
        summary_dropped = self.dropout(summary_vector)
        
        # Compute gate logits
        gate_logits = self.gate_projection(summary_dropped)
        
        # Apply softmax for normalized weights
        gate_weights = torch.softmax(gate_logits, dim=-1)
        
        return gate_weights


class SemanticGatingNetwork(AbstractGatingNetwork):
    """Gating network for semantic attractors.
    
    Per DeltaDecoder.md Section 3.1.2, computes softmax-normalized weights
    for semantic attractors based on the summary vector:
    g_Sem = Softmax(W_gSem * v_summary + b_gSem)
    
    Args:
        d_model: Model dimension
        num_attractors: Number of semantic attractors to gate
        dropout: Dropout probability for regularization
    """
    
    def __init__(
        self,
        d_model: int,
        num_attractors: int = DEFAULT_N_ATTRACTORS,
        dropout: float = 0.0
    ):
        super().__init__(d_model, num_attractors)
        
        # Gating projection: W_gSem and b_gSem from spec
        self.gate_projection = nn.Linear(d_model, num_attractors, bias=True)
        # Optional dropout for regularization (disabled by default for spec compliance)
        self.dropout = nn.Dropout(dropout) if dropout > 0.0 else nn.Identity()
        
        # Initialize weights for balanced initial gating
        self._initialize_weights()
    
    def _initialize_weights(self) -> None:
        """Initialize gating weights for balanced attractor selection."""
        # Use small random weights to encourage balanced initial selection
        nn.init.normal_(self.gate_projection.weight, mean=0.0, std=0.02)
        nn.init.zeros_(self.gate_projection.bias)
    
    def forward(self, summary_vector: Tensor) -> Tensor:
        """Compute semantic attractor gating weights.
        
        Args:
            summary_vector: Summary of the thesis (v_summary)
            
        Returns:
            Softmax-normalized gating weights of shape (batch_size, num_attractors)
        """
        # Apply dropout for regularization
        summary_dropped = self.dropout(summary_vector)
        
        # Compute gate logits
        gate_logits = self.gate_projection(summary_dropped)
        
        # Apply softmax for normalized weights
        gate_weights = torch.softmax(gate_logits, dim=-1)
        
        return gate_weights


class DualGatingNetwork(nn.Module):
    """Combined gating network for both structural and semantic attractors.
    
    This module combines both gating networks into a single component for
    convenience and ensures consistent processing of the summary vector.
    
    Args:
        d_model: Model dimension
        num_attractors: Number of attractors per domain
        dropout: Dropout probability for regularization
    """
    
    def __init__(
        self,
        d_model: int,
        num_attractors: int = DEFAULT_N_ATTRACTORS,
        dropout: float = 0.0
    ):
        super().__init__()
        
        self.d_model = d_model
        self.num_attractors = num_attractors
        
        # Initialize both gating networks
        self.structural_gate = StructuralGatingNetwork(d_model, num_attractors, dropout)
        self.semantic_gate = SemanticGatingNetwork(d_model, num_attractors, dropout)
    
    def forward(self, summary_vector: Tensor) -> tuple[Tensor, Tensor]:
        """Compute gating weights for both attractor domains.
        
        Args:
            summary_vector: Summary of the thesis (v_summary)
            
        Returns:
            Tuple of (structural_weights, semantic_weights), each of shape
            (batch_size, num_attractors)
        """
        # Compute gating weights for both domains
        structural_weights = self.structural_gate(summary_vector)
        semantic_weights = self.semantic_gate(summary_vector)
        
        return structural_weights, semantic_weights
    
    def get_global_loss_parameters(self) -> list:
        """Get parameters that should be updated by global task loss.
        
        Per Section 5, gating network weights are updated exclusively by global loss.
        
        Returns:
            List of parameters for global objective optimization
        """
        params = []
        params.extend(list(self.structural_gate.parameters()))
        params.extend(list(self.semantic_gate.parameters()))
        return params
