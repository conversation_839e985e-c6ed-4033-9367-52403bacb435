# An In-Depth Explanation of the DELTA Reasoning Architecture

This document provides a detailed walkthrough of the DELTA reasoning architecture, a novel framework designed to emulate a human-like cognitive process of **planning, thinking, and then speaking**. We will break down its components, operational phases, and the mathematical formalisms that underpin its functionality, based on the formal specification in [`docs/architecture/delta_architecture.md`](docs/architecture/delta_architecture.md:1).

## 1. High-Level Concept: Plan, Think, Speak

Unlike traditional autoregressive models that generate text token-by-token in a single continuous loop, the DELTA architecture separates the reasoning process into three distinct, sequential phases:

1.  **Phase 1: Planning**: The model first analyzes the user's prompt to create a high-level plan, or a `Conceptual Agenda`. This agenda consists of the distinct reasoning tasks required to formulate a complete response.
2.  **Phase 2: Conceptual Refinement (Thinking)**: For each task in the agenda, the model enters a non-verbal "thinking" loop. It uses a dialectical process of `Thesis -> Antithesis -> Synthesis` to iteratively refine its understanding of the concept, ensuring a robust and well-reasoned internal representation.
3.  **Phase 3: Verbalization (Speaking)**: Once a concept has been sufficiently refined, a specialized "Reader" module translates the final, stable internal representation into coherent, natural language.

This structured workflow is managed by a `PhaseManager` and is designed to produce outputs that are more logically sound, globally coherent, and faithful to complex instructions.

## 2. End-to-End Operational Workflow

The entire process, from receiving a prompt to generating a final response, follows a managed execution loop. The workflow is visualized in the following diagram:

```mermaid
graph TD
    A[Start: User Prompt] --> B{Phase 1: Planning};
    B --> C[Conceptual Agenda Created: O_1, O_2, ...];

    subgraph "Execution Loop for each Operator O_i in Agenda"
        direction TB
        C --> D{Select Task O_i};
        D --> E["Phase 2: Conceptual Refinement<br/>Internal Dialectic Loop"];
        E --> F[Stable Operator O_i_final];
        F --> G["Phase 3: Verbalization<br/>'Reader' Module probes Operator"];
        G --> H(Text chunk for Task i);
    end

    H --> C;
    C -- Agenda Empty --> I[Concatenate All Text Chunks];
    I --> J[Final Response];
```

Now, we will explore each of these phases and their underlying components in detail.
## 3. Phase 1: The Planner - Decomposing the Prompt

The first active phase of the DELTA architecture is **Planning**. The goal of the Planner is to analyze the input prompt and create a high-level strategic plan, called a `Conceptual Agenda`. This agenda breaks down a complex, multi-part prompt into a sequence of smaller, manageable reasoning tasks.

The process begins with a dedicated **Prefill Processor**.

### 3.1. The Prefill Processor

Before planning can occur, the input prompt is processed by a multi-layer recurrent network (specifically, a GRU). This processor reads the entire prompt sequentially and generates a sequence of contextualized hidden states ($\{\mathbf{h}_1, ..., \mathbf{h}_L\}$), one for each input token. These hidden states are crucial as they provide the rich, context-aware input needed for the subsequent planning steps.

### 3.2. Boundary Detection and Segmentation

With the hidden states generated, the model identifies the boundaries between distinct conceptual chunks within the prompt.

-   **Mechanism**: A specialized linear layer, the "boundary detection head," is applied to each hidden state $\mathbf{h}_t$. This produces a **boundary score** $s_t$ between 0 and 1, indicating the likelihood that token $t$ is the end of a conceptual segment.
    $$
    s_t = \sigma(\mathbf{w}_{b}^T \mathbf{h}_t + b_b)
    $$
-   **Segmentation**: The sequence of scores is then compared against a `boundary_threshold` hyperparameter. Any score exceeding this threshold marks a "split point," effectively segmenting the prompt's hidden states into $k$ distinct conceptual chunks. A fallback policy ensures that if no boundaries are detected, the entire prompt is treated as a single chunk.

### 3.3. Conceptual Operator Instantiation

For each conceptual chunk, a `ConceptualOperator` is created. This operator is the core data structure representing the "thought" or idea for that chunk.

-   **Mechanism**: The hidden states $\{\mathbf{h}_{j,1}, ..., \mathbf{h}_{j,m}\}$ corresponding to a chunk are first pooled (e.g., via mean pooling) into a single representative vector, $\bar{\mathbf{h}}_j$. This vector is then projected by two separate linear layers to generate the two low-rank matrices, $\mathbf{A}_j$ and $\mathbf{B}_j$, that define the operator $\mathbf{O}_j = \mathbf{A}_j\mathbf{B}_j^T$.
    $$
    \text{vec}(\mathbf{A}_j) = \mathbf{W}_{inst,A} \bar{\mathbf{h}}_j
    $$
    $$
    \text{vec}(\mathbf{B}_j) = \mathbf{W}_{inst,B} \bar{\mathbf{h}}_j
    $$
    This learnable process ensures that concepts are instantiated into a consistent and meaningful structure.

### 3.4. Manifold Construction: The Mind Map

The final and most sophisticated step of the Planner is to arrange these newly created operators onto a **Conceptual Manifold**. This is a low-dimensional geometric space that acts as a "mind map," encoding the relationships between the different concepts.

-   **The Cartographer**: A dedicated feed-forward network, the `Cartographer`, takes the pooled hidden state for each chunk ($\bar{\mathbf{h}}_i$) and computes its coordinates ($\mathbf{c}_i$) on the manifold.
    $$
    \mathbf{c}_i = \text{Cartographer}(\bar{\mathbf{h}}_i)
    $$
-   **Encoding Relationships**: The geometry of the manifold encodes meaning:
    -   **Similarity**: The Euclidean distance $\|\mathbf{c}_i - \mathbf{c}_j\|$ represents semantic similarity. The model is trained to place related concepts closer together.
    -   **Dependency**: A directional axis on the manifold can represent logical flow or prerequisite relationships.
-   **Training the Geometry**: This meaningful arrangement is not accidental. The `Cartographer` is trained explicitly with a composite **Placement Loss ($\mathcal{L}_{place}$)**, which is the weighted sum of a similarity loss and a dependency loss.
    $$
    \mathcal{L}_{place} = \alpha \mathcal{L}_{sim} + \beta \mathcal{L}_{dep}
    $$
    -   $\mathcal{L}_{sim}$ is a contrastive loss that pulls similar concepts together and pushes dissimilar ones apart.
    -   $\mathcal{L}_{dep}$ ensures that if concept $j$ depends on concept $i$, it appears "after" $i$ along a designated axis on the manifold.

The final output of the Planner is this `ConceptualManifold`—a collection of `(Operator, Coordinates)` tuples that represents a structured, high-level plan for generating the final response. The model will then process these operators according to a path along this manifold.
## 4. The Conceptual Operator: The Anatomy of a Thought

At the heart of the DELTA architecture is the `ConceptualOperator`. This is not a static vector but a dynamic, learnable linear transformation that represents a "thought."

-   **Formalism**: It is a low-rank operator, $\mathbf{O} \in \mathbb{R}^{d \times d}$, defined by the product of two smaller, "thin" matrices, $\mathbf{A}, \mathbf{B} \in \mathbb{R}^{d \times r}$, where $r$ (the rank) is much smaller than $d$.
    $$
    \mathbf{O} = \mathbf{A}\mathbf{B}^T
    $$
-   **Function**: This operator acts as an **addressable semantic space**. It can be "probed" by applying it to vectors, and its internal structure (the columns of $\mathbf{A}$ and $\mathbf{B}$) encodes the underlying features of the concept it represents. The rank $r$ is a crucial hyperparameter (`operator_rank_r`) that determines the expressive capacity of a single thought.

## 5. Phase 2: The Conceptual Refinement Loop - The "Thinking" Process

Once the Planner has created an agenda of operators, the model begins the **Conceptual Refinement Loop** for each one. This is an internal, non-verbal "thinking" process where the operator itself is iteratively improved.

The core of this phase is a dialectical process: `Thesis -> Antithesis -> Synthesis`. An initial idea (Thesis) is challenged by counterarguments (Antithesis), and the conflict is resolved into a more refined idea (Synthesis). This new synthesis then becomes the thesis for the next iteration.

This entire process is detailed in **Algorithm 1** from the specification. Let's break down its key steps.

### 5.1. Algorithm 1: A Walkthrough

**Input**: An initial operator $\mathbf{O}^{(0)}$ from the Planner.
**Goal**: Iteratively refine the operator over $N_{refine}$ steps to produce a stable, well-reasoned final operator, $\mathbf{O}_{final}$.

1.  **Generate Candidate Theses**: At each step $n$, instead of working with a single idea, the model explores multiple "what-if" scenarios. It uses a set of `B` learnable **probe vectors** ($\{\mathbf{v}_{probe,b}\}$) to query the current operator $\mathbf{O}^{(n)}$. Each probe "asks" about the concept from a different angle, generating a set of candidate theses.
    $$
    \text{thesis}_b = \mathbf{O}^{(n)}(\mathbf{v}_{probe,b})
    $$

2.  **Pre-selection via Entropy**: To work efficiently, the model performs a cheap pre-selection to discard unpromising theses. It calculates the verbalization entropy for each thesis—a measure of its ambiguity. Only the theses with the lowest entropy (the "clearest" thoughts) are kept as `active_theses` for the next, more expensive step.

3.  **Parallel Refinement (The Dialectic Engine)**: Each `active_thesis` is now passed through the core dialectic engine in parallel.
    *   **Antithesis Engine**: For a given `thesis` ($\mathbf{V}_T$), this engine generates a set of challenging "force vectors" ($\mathbf{F}_{all}$). It does this using two banks of "attractors":
        *   **Structural Attractors**: Stateless experts that represent context-free rules like logic and grammar.
        *   **Semantic Attractors**: Stateful, more complex experts that track context and semantic constraints.
    *   **Synthesis Module**: This module's job is to resolve the tension. It uses **cross-attention**, with the `thesis` vector as the Query and the `Antithesis` force vectors as the Keys and Values. This allows the model to dynamically select and weigh the most relevant corrective forces to apply. The result is a new, refined **Synthesis vector** ($\mathbf{V}_S$).
        $$ \mathbf{V}_{S,raw} = \text{MultiHeadCrossAttention}(\mathbf{q}=\mathbf{V}_T, \mathbf{k}=\mathbf{F}_{all}, \mathbf{v}=\mathbf{F}_{all}) $$
        $$ \mathbf{V}_{S} = \text{LayerNorm}(\mathbf{V}_T + \mathbf{V}_{S,raw}) $$

4.  **Selection via Inner Monologue**: The model now has a set of `synthesized_states`. To choose the best one, it "votes" by generating a very short, internal monologue for each state using a lightweight `InnerVerbalizer`. A `ConfidenceScorer` then evaluates each path based on the fluency of this monologue and the internal consistency of the state itself. The path with the highest confidence score wins.

5.  **Compute Loss and Update Operator**: The winning thesis ($\mathbf{V}_{T,best}$) and its corresponding synthesis ($\mathbf{V}_{S,best}$) are used to calculate a `refinement_loss`. This loss has two parts:
    *   $\mathcal{L}_{correction}$: A correction loss that pushes the *input* to the next loop step to be closer to the *output* of the current step ($\|\mathbf{V}_{S,best} - \mathbf{V}_{T,best}\|^2$). This encourages the process to stabilize.
    *   $\mathcal{L}_{confidence}$: A loss that encourages the confidence scorer to be decisive in its winning choice.
    
    The gradient of this total loss is used to update the matrices ($\mathbf{A}$ and $\mathbf{B}$) of the `ConceptualOperator`, thereby refining the "thought" itself.

6.  **Stability Checks**: The process includes checks for convergence (the operator update is very small) or divergence (the update is too large), allowing for early stopping.

This loop continues for $N_{refine}$ steps or until a stability condition is met, resulting in a final, refined operator ready for verbalization.
## 6. Phase 3: The "Reader" Verbalizer - Speaking the Thought

After the refinement loop produces a stable, final operator ($\mathbf{O}_{final}$), the **Verbalizer** module takes over. Its job is to translate the rich, structured information contained within the operator into a coherent sequence of text.

This process is not statistical "guessing." Instead, it is framed as actively **"reading"** the contents of the operator.

### 6.1. Algorithm 2: The Verbalization Loop

The Verbalizer is a simple recurrent module (e.g., a GRU) that executes the following loop:

1.  **Initialization**: The Verbalizer's recurrent state ($\mathbf{h}_{verb}$) is initialized. This state acts as a "reading head" or a pointer to a specific location within the conceptual space of the operator.
2.  **Iterative Probing**: The loop proceeds token by token:
    a.  The recurrent state $\mathbf{h}_{verb}$ is updated with the embedding of the previously generated token.
    b.  This updated state is then used to **probe** the final operator:
        $$
        \mathbf{v}_{probed} = \mathbf{O}_{final}(\mathbf{h}_{verb, t+1})
        $$
    c.  The resulting vector, $\mathbf{v}_{probed}$, which represents the content "read" from that part of the operator, is projected to the vocabulary space to produce logits for the next token.
    d.  A new token is sampled, and the loop continues.
3.  **Termination**: The loop continues until an End-of-Sequence (`[EOS]`) token is generated or a maximum length is reached.

Crucially, the coherence between the operator and the verbalizer is enforced during end-to-end training. Gradients from the verbalization loss flow back to both the Verbalizer's parameters and the Operator's instantiation layers, forcing them to learn a compatible, shared language of probing and representation. This mechanism constrains the Verbalizer to the semantic space defined by the refined operator, acting as a primary defense against hallucination.

## 7. The Phase Manager: Orchestrating the Entire Process

The `PhaseManager` is the high-level control logic that manages the entire workflow from prompt to final text, as detailed in **Algorithm 3**. It ensures that the phases execute in the correct order and handles the flow of information between them.

1.  **Planning**: It first calls the `Planner` to produce the `ConceptualManifold`.
2.  **Path Traversal**: It determines a logical traversal path across the manifold, ensuring that operators are processed in a coherent order (e.g., following dependencies).
3.  **Execution Loop**: It iterates through the operators in the determined order. For each operator, it:
    a.  **Applies Cohesion**: It adjusts the current operator based on the state of its already-processed neighbors on the manifold. This ensures a smooth transition between related concepts.
    b.  **Invokes Refinement**: It runs the `Conceptual Refinement Loop` on the cohesive operator.
    c.  **Handles Re-planning**: If the refinement process determines a concept is too complex and needs to be split, the `PhaseManager` can pause, articulate the complex thought into an internal prompt, and re-run the Planner on that prompt to generate a more granular plan.
    d.  **Invokes Verbalization**: It calls the `Verbalizer` on the final, refined operator to generate the text for that chunk.
4.  **Concatenation**: Once all operators in the agenda have been processed, the `PhaseManager` concatenates the generated text chunks in their original order to produce the final, complete response.
## 8. Implementation, Training, and Lifelong Learning

Beyond the core reasoning pipeline, the DELTA architecture specifies several key components and processes required for its practical implementation, training, and long-term adaptation.

### 8.1. Training and Evaluation

-   **Training Data and Loss**: The model is trained end-to-end on a specialized dataset containing prompts, a `target_texts` for each conceptual chunk, and the ground-truth `boundary_targets` and relationship matrices needed for the Planner. The total loss ($\mathcal{L}_{total}$) is a weighted sum of four distinct components:
    1.  **$\mathcal{L}_{verb}$ (Verbalization Loss)**: The primary loss, driving the model to produce the correct text.
    2.  **$\mathcal{L}_{plan}$ (Planner Loss)**: Trains the boundary detection head.
    3.  **$\mathcal{L}_{place}$ (Placement Loss)**: Trains the `Cartographer` to build a meaningful mind map.
    4.  **$\mathcal{L}_{conv}$ (Convergence Loss)**: A regularizer that encourages the refinement loop to find stable solutions.
-   **Evaluation**: Evaluation focuses on multi-step reasoning (e.g., GSM8K, BBH), logical reasoning (LogiQA), and instruction following. Crucially, it also involves metrics that measure the performance of internal components, such as the Planner's F1-score for boundary detection and the Refinement Loop's convergence rate.

### 8.2. Lifelong Learning: A Three-Stage Skill Lifecycle

A key feature of the DELTA architecture is its ability to learn and adapt over time without full retraining. This is achieved through a three-stage skill lifecycle managed by an external **Reward Model** that judges the success or failure of a generation.

1.  **Stage 1: Imprinting (Short-Term Memory)**: When a generation is deemed successful, the final, refined `ConceptualOperator` that produced it is "imprinted" into a temporary `KnowledgeBase`. This allows the model to immediately reuse successful lines of thought within the same session.

2.  **Stage 2: Promotion (Long-Term Memory)**: If an imprinted operator is used successfully multiple times (crossing a `promotion_threshold`), it gets **promoted**. Its structure is copied into the parameters of one of the permanent, learnable `Attractor` slots in the `AntithesisEngine`. This transforms a specific, successful thought into a general, reusable reasoning skill that the model can apply in the future.

3.  **Stage 3: Demotion (Forgetting)**: To prevent rigidity and unlearn ineffective skills, the model also tracks failures. If a permanent `Attractor` is frequently involved in failed generations (crossing a `demotion_threshold`), it gets **demoted**. Its parameters are reset, and the slot becomes inactive, ready to learn a new skill.

This lifecycle allows the DELTA model to remain stable while dynamically growing its set of reasoning experts, adapting to new information, and discarding patterns that are no longer useful.