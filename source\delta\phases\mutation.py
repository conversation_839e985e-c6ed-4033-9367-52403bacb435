"""Mutation Phase Implementation

This module implements the Mutation Phase as described in Section 4.3 of the DELTA specification.
"""

import torch
import torch.nn as nn
from typing import List, Optional, Dict
import logging

from ..core.base.types import DELTAConfig, PhaseOutput
from ..core.base.abstract_implementations import AbstractMutatePhase
from ..core.delta_stack import DE<PERSON>AStack
from ..utils.constants import SUCCESS_THRESHOLD

logger = logging.getLogger(__name__)


class MutationPhase(AbstractMutatePhase):
    """Implements the Mutation Phase (§4.3) - "The Adaptation"
    
    The Mutation Phase represents the third component of the DELTA operational cycle,
    following Prefill and Decoding. When a generation trajectory is deemed successful
    (based on task-specific metrics), this phase extracts a "Minimal-State-Essence" ε
    from the synthesis trajectory and fuses it back into the semantic attractor states.
    
    This mechanism allows the model to adapt its reasoning horizon based on successful
    generation patterns, embodying a form of inference-time learning that improves
    performance on complex reasoning tasks.
    
    Algorithm (§4.3):
    1. Extract essence ε from successful synthesis trajectory via averaging
    2. Apply horizon mutation: h'_{sem,i,j} = (1-λ)·h_{sem,i,j} + λ·ε
    3. Update semantic attractor states with configurable fusion rate λ
    
    Args:
        config: DELTA model configuration containing mutation parameters
        delta_stack: Stack of DELTA blocks for state management
    """
    
    def __init__(self, config: DELTAConfig, delta_stack: DELTAStack):
        self.config = config
        self.delta_stack = delta_stack
        self.logger = logger
        
    def execute(
        self,
        synthesis_history: List[torch.Tensor],
        semantic_states: Dict[int, List[torch.Tensor]],
        generated_tokens: Optional[List[int]] = None
    ) -> PhaseOutput:
        """Execute the Mutation Phase to adapt reasoning horizon.
        
        Implements the complete mutation algorithm from §4.3:
        1. Validates synthesis trajectory is non-empty
        2. Extracts minimal-state-essence ε via temporal averaging
        3. Applies horizon mutation with configurable fusion rate λ
        4. Updates semantic attractor states across all layers
        
        Args:
            synthesis_history: List of synthesis vectors V_s from successful trajectory
            semantic_states: Current semantic attractor states per layer
            generated_tokens: Optional token sequence for trajectory analysis
            
        Returns:
            PhaseOutput containing mutation results and updated states
        """
        
        if not synthesis_history:
            return PhaseOutput(
                final_state=None,
                mutation_applied=False,
                success_score=0.0,
                semantic_states=semantic_states
            )
        
        # Step 1: Extract essence from synthesis history
        essence = self._extract_essence(synthesis_history)
        
        # Step 2: Apply horizon mutation
        mutated_states = self._apply_horizon_mutation(semantic_states, essence)
        
        return PhaseOutput(
            final_state=essence,
            mutation_applied=True,
            success_score=1.0,
            semantic_states=mutated_states
        )
    
    def _extract_essence(self, synthesis_states: List[torch.Tensor]) -> torch.Tensor:
        """Extract minimal state essence from synthesis trajectory.
        
        Implements the essence function E(V_s, h_t, s, L) from §4.3 using
        temporal averaging across the synthesis trajectory. This creates
        a compact representation of successful generation patterns.
        
        Args:
            synthesis_states: List of synthesis vectors from trajectory
            
        Returns:
            Extracted essence vector ε of shape (d_model,)
        """
        stacked_states = torch.stack(synthesis_states, dim=0)
        essence = torch.mean(stacked_states, dim=0)
        
        if essence.dim() > 1:
            essence = torch.mean(essence, dim=0)
            
        return essence
    
    def _apply_horizon_mutation(
        self, 
        semantic_states: Dict[int, List[torch.Tensor]], 
        essence: torch.Tensor,
        lambda_rate: Optional[float] = None
    ) -> Dict[int, List[torch.Tensor]]:
        """Apply essence to semantic attractor states with configurable fusion rate.
        
        Implements the horizon mutation mechanism from §4.3:
        h'_{sem,i,j} = (1 - λ) · h_{sem,i,j} + λ · ε
        
        Args:
            semantic_states: Current semantic attractor states
            essence: Extracted essence vector ε
            lambda_rate: Fusion rate λ (default from config.mutation_rate)
        """
        if lambda_rate is None:
            lambda_rate = getattr(self.config, 'mutation_rate', 0.1)
        
        mutated_states = {}
        
        for layer_idx in range(self.config.n_layers):
            current_states = semantic_states.get(layer_idx, None)
            
            if current_states is not None:
                batch_size = current_states[0].shape[0]
                
                # Expand essence to match state dimensions if needed
                if essence.dim() == 1:
                    essence_expanded = essence.unsqueeze(0).expand(batch_size, -1)
                else:
                    essence_expanded = essence
                
                # Apply configurable fusion: h' = (1 - λ) * h + λ * ε
                mutated_layer_states = []
                for state in current_states:
                    # Ensure essence matches state device and dtype
                    essence_aligned = essence_expanded.to(
                        device=state.device, 
                        dtype=state.dtype
                    )
                    
                    # Apply horizon mutation with fusion
                    mutated_state = (1 - lambda_rate) * state + lambda_rate * essence_aligned
                    mutated_layer_states.append(mutated_state)
                
                mutated_states[layer_idx] = mutated_layer_states
            else:
                # Initialize fresh states if none exist
                delta_block = self.delta_stack.layers[layer_idx]
                fresh_states = delta_block.reset_semantic_states_for_mutation(
                    batch_size=1,
                    essence=essence
                )
                mutated_states[layer_idx] = list(fresh_states.values())
        
        return mutated_states