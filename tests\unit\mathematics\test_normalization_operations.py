"""
Comprehensive test suite for normalization operations in DELTA architecture.

This module tests normalization functions used throughout the DELTA blocks,
including LayerNorm operations specified in the refinement stage and other
normalization techniques for numerical stability.

Tests follow TDD principles and validate specification adherence.
"""

import pytest
import torch
import numpy as np
from typing import Tuple, Optional
import math

from source.delta.components.normalization import (
    LayerNorm,
    RMSNorm,
    GroupNorm,
    AdaptiveLayerNorm,
    PowerNorm,
    ScaleNorm,
    CrossNorm,
    batch_norm_1d,
    instance_norm,
    spectral_norm
    # gradient_normalization removed - not part of DELTA specification
)


class TestLayerNorm:
    """Test LayerNorm implementation as used in DELTA refinement stage."""
    
    def setup_method(self):
        """Set up test fixtures."""
        torch.manual_seed(42)
        self.batch_size = 4
        self.seq_len = 8
        self.d_model = 64
        self.eps = 1e-5
        
        # Standard input tensor
        self.input_tensor = torch.randn(self.batch_size, self.seq_len, self.d_model)
        
        # LayerNorm modules
        self.layer_norm = LayerNorm(self.d_model, eps=self.eps)
        self.torch_layer_norm = torch.nn.LayerNorm(self.d_model, eps=self.eps)
    
    def test_layer_norm_equation_correctness(self):
        """Test LayerNorm matches the mathematical specification."""
        # Manual implementation of LayerNorm equation
        x = self.input_tensor
        mean = x.mean(dim=-1, keepdim=True)
        var = x.var(dim=-1, keepdim=True, unbiased=False)
        expected = (x - mean) / torch.sqrt(var + self.eps)
        
        # Add learnable parameters
        expected = expected * self.layer_norm.weight + self.layer_norm.bias
        
        # Test our implementation
        result = self.layer_norm(self.input_tensor)
        
        torch.testing.assert_close(result, expected,  rtol=1e-7, atol=1e-6)
    
    def test_layer_norm_pytorch_consistency(self):
        """Test consistency with PyTorch's LayerNorm."""
        # Initialize with same parameters
        with torch.no_grad():
            self.torch_layer_norm.weight.copy_(self.layer_norm.weight)
            self.torch_layer_norm.bias.copy_(self.layer_norm.bias)
        
        our_result = self.layer_norm(self.input_tensor)
        torch_result = self.torch_layer_norm(self.input_tensor)
        
        torch.testing.assert_close(our_result, torch_result,  rtol=1e-7, atol=1e-6)
    
    def test_layer_norm_properties(self):
        """Test LayerNorm mathematical properties."""
        result = self.layer_norm(self.input_tensor)
        
        # Output should have zero mean and unit variance along last dimension
        # (before applying weight and bias)
        x = self.input_tensor
        mean = x.mean(dim=-1, keepdim=True)
        var = x.var(dim=-1, keepdim=True, unbiased=False)
        normalized = (x - mean) / torch.sqrt(var + self.eps)
        
        # Check normalized values (before weight/bias)
        norm_mean = normalized.mean(dim=-1)
        norm_var = normalized.var(dim=-1, unbiased=False)
        
        torch.testing.assert_close(norm_mean, torch.zeros_like(norm_mean), rtol=1e-6, atol=1e-5)
        torch.testing.assert_close(norm_var, torch.ones_like(norm_var), rtol=1e-5, atol=1e-4)
    
    def test_layer_norm_gradient_flow(self):
        """Test gradient flow through LayerNorm."""
        x = self.input_tensor.clone().requires_grad_(True)
        y = self.layer_norm(x)
        loss = y.sum()
        loss.backward()
        
        assert x.grad is not None
        assert torch.isfinite(x.grad).all()
        
        # Parameters should receive gradients
        assert self.layer_norm.weight.grad is not None
        assert self.layer_norm.bias.grad is not None
        assert torch.isfinite(self.layer_norm.weight.grad).all()
        assert torch.isfinite(self.layer_norm.bias.grad).all()
    
    def test_layer_norm_numerical_stability(self):
        """Test LayerNorm numerical stability with edge cases."""
        # Test with constant input (zero variance)
        constant_input = torch.full((self.batch_size, self.seq_len, self.d_model), 5.0)
        result = self.layer_norm(constant_input)
        assert torch.isfinite(result).all()
        
        # Test with very small variance
        small_var_input = self.input_tensor * 1e-8
        result = self.layer_norm(small_var_input)
        assert torch.isfinite(result).all()
        
        # Test with very large values
        large_input = self.input_tensor * 1e6
        result = self.layer_norm(large_input)
        assert torch.isfinite(result).all()
    
    def test_layer_norm_shape_preservation(self):
        """Test that LayerNorm preserves input shape."""
        result = self.layer_norm(self.input_tensor)
        assert result.shape == self.input_tensor.shape
    
    def test_layer_norm_delta_refinement_stage(self):
        """Test LayerNorm in DELTA refinement stage context."""
        # Simulate Stage 4 refinement from DELTA specification
        # h' = LayerNorm(V_T + V_S,raw)
        # V_S = LayerNorm(h' + FFN(h'))
        
        V_T = torch.randn(self.batch_size, self.d_model)  # Thesis
        V_S_raw = torch.randn(self.batch_size, self.d_model)  # Raw synthesis
        
        # First LayerNorm
        h_prime = self.layer_norm(V_T + V_S_raw)
        assert h_prime.shape == V_T.shape
        assert torch.isfinite(h_prime).all()
        
        # FFN simulation (just a linear layer for testing)
        ffn_weight = torch.randn(self.d_model, self.d_model * 4)
        ffn_bias = torch.randn(self.d_model * 4)
        ffn_output = torch.matmul(h_prime, ffn_weight) + ffn_bias
        ffn_output = torch.relu(ffn_output)
        
        ffn_weight2 = torch.randn(self.d_model * 4, self.d_model)
        ffn_bias2 = torch.randn(self.d_model)
        ffn_final = torch.matmul(ffn_output, ffn_weight2) + ffn_bias2
        
        # Second LayerNorm
        layer_norm2 = LayerNorm(self.d_model, eps=self.eps)
        V_S = layer_norm2(h_prime + ffn_final)
        
        assert V_S.shape == V_T.shape
        assert torch.isfinite(V_S).all()


class TestRMSNorm:
    """Test Root Mean Square Normalization."""
    
    def setup_method(self):
        """Set up test fixtures."""
        torch.manual_seed(42)
        self.batch_size = 4
        self.seq_len = 8
        self.d_model = 64
        self.eps = 1e-8
        
        self.input_tensor = torch.randn(self.batch_size, self.seq_len, self.d_model)
        self.rms_norm = RMSNorm(self.d_model, eps=self.eps)
    
    def test_rms_norm_equation(self):
        """Test RMSNorm mathematical equation."""
        # RMSNorm: x / sqrt(mean(x^2) + eps) * weight
        x = self.input_tensor
        rms = torch.sqrt(torch.mean(x ** 2, dim=-1, keepdim=True) + self.eps)
        expected = (x / rms) * self.rms_norm.weight
        
        result = self.rms_norm(self.input_tensor)
        torch.testing.assert_close(result, expected,  rtol=1e-7, atol=1e-6)
    
    def test_rms_norm_properties(self):
        """Test RMSNorm properties."""
        result = self.rms_norm(self.input_tensor)
        
        # RMSNorm should preserve input shape
        assert result.shape == self.input_tensor.shape
        
        # Should be numerically stable
        assert torch.isfinite(result).all()
        
        # RMS of normalized tensor should be approximately 1 (before weight scaling)
        x = self.input_tensor
        rms = torch.sqrt(torch.mean(x ** 2, dim=-1, keepdim=True) + self.eps)
        normalized = x / rms
        normalized_rms = torch.sqrt(torch.mean(normalized ** 2, dim=-1))
        
        torch.testing.assert_close(
            normalized_rms, torch.ones_like(normalized_rms), rtol=1e-4, atol=1e-5
        )
    
    def test_rms_norm_vs_layer_norm(self):
        """Test RMSNorm vs LayerNorm behavior differences."""
        layer_norm = LayerNorm(self.d_model)
        
        rms_result = self.rms_norm(self.input_tensor)
        layer_result = layer_norm(self.input_tensor)
        
        # Results should be different (RMSNorm doesn't center)
        assert not torch.allclose(rms_result, layer_result, rtol=1e-2)
        
        # But both should be stable
        assert torch.isfinite(rms_result).all()
        assert torch.isfinite(layer_result).all()
    
    def test_rms_norm_gradient_flow(self):
        """Test gradient flow through RMSNorm."""
        x = self.input_tensor.clone().requires_grad_(True)
        y = self.rms_norm(x)
        loss = y.sum()
        loss.backward()
        
        assert x.grad is not None
        assert torch.isfinite(x.grad).all()
        assert self.rms_norm.weight.grad is not None
        assert torch.isfinite(self.rms_norm.weight.grad).all()


class TestGroupNorm:
    """Test Group Normalization."""
    
    def setup_method(self):
        """Set up test fixtures."""
        torch.manual_seed(42)
        self.batch_size = 4
        self.num_channels = 32
        self.height = 8
        self.width = 8
        self.num_groups = 8
        
        # GroupNorm typically used with channel-first format
        self.input_tensor = torch.randn(self.batch_size, self.num_channels, self.height, self.width)
        self.group_norm = GroupNorm(self.num_groups, self.num_channels)
    
    def test_group_norm_equation(self):
        """Test GroupNorm mathematical equation."""
        result = self.group_norm(self.input_tensor)
        
        # GroupNorm should normalize within groups
        assert result.shape == self.input_tensor.shape
        assert torch.isfinite(result).all()
        
        # Check that groups have approximately zero mean and unit variance
        N, C, H, W = self.input_tensor.shape
        G = self.num_groups
        
        # Reshape to separate groups
        x_grouped = self.input_tensor.view(N, G, C // G, H, W)
        result_grouped = result.view(N, G, C // G, H, W)
        
        # Each group should be normalized (before weight/bias)
        # This is conceptual - exact verification depends on implementation details
    
    def test_group_norm_pytorch_consistency(self):
        """Test consistency with PyTorch GroupNorm."""
        torch_group_norm = torch.nn.GroupNorm(self.num_groups, self.num_channels)
        
        # Initialize with same parameters
        with torch.no_grad():
            torch_group_norm.weight.copy_(self.group_norm.weight)
            torch_group_norm.bias.copy_(self.group_norm.bias)
        
        our_result = self.group_norm(self.input_tensor)
        torch_result = torch_group_norm(self.input_tensor)
        
        torch.testing.assert_close(our_result, torch_result,  rtol=1e-6, atol=1e-5)
    
    def test_group_norm_1d_input(self):
        """Test GroupNorm with 1D input (for DELTA use case)."""
        # Adapt GroupNorm for sequence data
        seq_input = torch.randn(self.batch_size, self.num_channels)
        
        # Reshape for GroupNorm and back
        reshaped_input = seq_input.unsqueeze(-1).unsqueeze(-1)  # Add H, W dimensions
        result = self.group_norm(reshaped_input)
        final_result = result.squeeze(-1).squeeze(-1)
        
        assert final_result.shape == seq_input.shape
        assert torch.isfinite(final_result).all()


class TestAdaptiveLayerNorm:
    """Test Adaptive Layer Normalization."""
    
    def setup_method(self):
        """Set up test fixtures."""
        torch.manual_seed(42)
        self.batch_size = 4
        self.seq_len = 8
        self.d_model = 64
        self.condition_dim = 32
        
        self.input_tensor = torch.randn(self.batch_size, self.seq_len, self.d_model)
        self.condition = torch.randn(self.batch_size, self.condition_dim)
        self.adaptive_norm = AdaptiveLayerNorm(self.d_model, self.condition_dim)
    
    def test_adaptive_layer_norm_forward(self):
        """Test AdaptiveLayerNorm forward pass."""
        result = self.adaptive_norm(self.input_tensor, self.condition)
        
        assert result.shape == self.input_tensor.shape
        assert torch.isfinite(result).all()
    
    def test_adaptive_layer_norm_conditioning(self):
        """Test that conditioning affects normalization."""
        condition1 = torch.randn(self.batch_size, self.condition_dim)
        condition2 = torch.randn(self.batch_size, self.condition_dim)
        
        result1 = self.adaptive_norm(self.input_tensor, condition1)
        result2 = self.adaptive_norm(self.input_tensor, condition2)
        
        # Different conditions should produce different results
        assert not torch.allclose(result1, result2, rtol=1e-3)
    
    def test_adaptive_layer_norm_gradient_flow(self):
        """Test gradient flow through adaptive normalization."""
        x = self.input_tensor.clone().requires_grad_(True)
        condition = self.condition.clone().requires_grad_(True)
        
        y = self.adaptive_norm(x, condition)
        loss = y.sum()
        loss.backward()
        
        assert x.grad is not None
        assert condition.grad is not None
        assert torch.isfinite(x.grad).all()
        assert torch.isfinite(condition.grad).all()


class TestPowerNorm:
    """Test Power Normalization."""
    
    def setup_method(self):
        """Set up test fixtures."""
        torch.manual_seed(42)
        self.batch_size = 4
        self.seq_len = 8
        self.d_model = 64
        self.power = 2.0
        
        self.input_tensor = torch.randn(self.batch_size, self.seq_len, self.d_model)
        self.power_norm = PowerNorm(self.d_model, power=self.power)
    
    def test_power_norm_equation(self):
        """Test PowerNorm mathematical equation."""
        result = self.power_norm(self.input_tensor)
        
        # PowerNorm: x / (mean(|x|^p))^(1/p)
        x = self.input_tensor
        power_mean = torch.mean(torch.abs(x) ** self.power, dim=-1, keepdim=True)
        norm_factor = torch.pow(power_mean, 1.0 / self.power)
        expected = x / (norm_factor + 1e-8)  # Add epsilon for stability
        
        # Account for learnable scaling
        expected = expected * self.power_norm.weight
        
        torch.testing.assert_close(result, expected, rtol=1e-5, atol=1e-4)
    
    def test_power_norm_properties(self):
        """Test PowerNorm properties with different powers."""
        powers = [1.0, 2.0, 4.0]
        
        for power in powers:
            power_norm = PowerNorm(self.d_model, power=power)
            result = power_norm(self.input_tensor)
            
            assert result.shape == self.input_tensor.shape
            assert torch.isfinite(result).all()
    
    def test_power_norm_special_cases(self):
        """Test PowerNorm special cases."""
        # p=2 should be similar to L2 normalization
        power_norm_2 = PowerNorm(self.d_model, power=2.0)
        result_2 = power_norm_2(self.input_tensor)
        
        # p=1 should be L1 normalization
        power_norm_1 = PowerNorm(self.d_model, power=1.0)
        result_1 = power_norm_1(self.input_tensor)
        
        # Both should be stable and different
        assert torch.isfinite(result_1).all()
        assert torch.isfinite(result_2).all()
        assert not torch.allclose(result_1, result_2, rtol=1e-2)


class TestScaleNorm:
    """Test Scale Normalization."""
    
    def setup_method(self):
        """Set up test fixtures."""
        torch.manual_seed(42)
        self.batch_size = 4
        self.seq_len = 8
        self.d_model = 64
        
        self.input_tensor = torch.randn(self.batch_size, self.seq_len, self.d_model)
        self.scale_norm = ScaleNorm(self.d_model)
    
    def test_scale_norm_equation(self):
        """Test ScaleNorm mathematical equation."""
        result = self.scale_norm(self.input_tensor)
        
        # ScaleNorm: x / ||x||_2 * scale
        x = self.input_tensor
        l2_norm = torch.norm(x, dim=-1, keepdim=True)
        expected = (x / (l2_norm + 1e-8)) * self.scale_norm.scale
        
        torch.testing.assert_close(result, expected,  rtol=1e-7, atol=1e-6)
    
    def test_scale_norm_unit_norm_property(self):
        """Test that ScaleNorm produces unit norm vectors."""
        result = self.scale_norm(self.input_tensor)
        
        # Before scaling, should have unit norm
        x = self.input_tensor
        l2_norm = torch.norm(x, dim=-1, keepdim=True)
        normalized = x / (l2_norm + 1e-8)
        normalized_norms = torch.norm(normalized, dim=-1)
        
        torch.testing.assert_close(
            normalized_norms, torch.ones_like(normalized_norms), rtol=1e-6, atol=1e-5
        )
    
    def test_scale_norm_learnable_scale(self):
        """Test that scale parameter is learnable."""
        x = self.input_tensor.clone().requires_grad_(True)
        y = self.scale_norm(x)
        loss = y.sum()
        loss.backward()
        
        assert self.scale_norm.scale.grad is not None
        assert torch.isfinite(self.scale_norm.scale.grad).all()


class TestCrossNorm:
    """Test Cross Normalization for multi-modal inputs."""
    
    def setup_method(self):
        """Set up test fixtures."""
        torch.manual_seed(42)
        self.batch_size = 4
        self.seq_len = 8
        self.d_model = 64
        
        self.input_tensor = torch.randn(self.batch_size, self.seq_len, self.d_model)
        self.reference_tensor = torch.randn(self.batch_size, self.seq_len, self.d_model)
        self.cross_norm = CrossNorm(self.d_model)
    
    def test_cross_norm_forward(self):
        """Test CrossNorm forward pass."""
        result1, result2 = self.cross_norm(self.input_tensor, self.reference_tensor)

        assert result1.shape == self.input_tensor.shape
        assert result2.shape == self.reference_tensor.shape
        assert torch.isfinite(result1).all()
        assert torch.isfinite(result2).all()
    
    def test_cross_norm_reference_dependency(self):
        """Test that output depends on reference tensor."""
        reference1 = torch.randn(self.batch_size, self.seq_len, self.d_model)
        reference2 = torch.randn(self.batch_size, self.seq_len, self.d_model)
        
        result1_1, result1_2 = self.cross_norm(self.input_tensor, reference1)
        result2_1, result2_2 = self.cross_norm(self.input_tensor, reference2)

        # Different references should produce different results
        # CrossNorm creates cross-dependencies, so both outputs should differ
        assert not torch.allclose(result1_1, result2_1, rtol=1e-3, atol=1e-3)
        assert not torch.allclose(result1_2, result2_2, rtol=1e-3, atol=1e-3)
    
    def test_cross_norm_gradient_flow(self):
        """Test gradient flow through both inputs."""
        x = self.input_tensor.clone().requires_grad_(True)
        ref = self.reference_tensor.clone().requires_grad_(True)

        y1, y2 = self.cross_norm(x, ref)
        loss = y1.sum() + y2.sum()
        loss.backward()

        assert x.grad is not None
        assert ref.grad is not None
        assert torch.isfinite(x.grad).all()
        assert torch.isfinite(ref.grad).all()


class TestNormalizationUtilities:
    """Test utility normalization functions."""
    
    def setup_method(self):
        """Set up test fixtures."""
        torch.manual_seed(42)
        self.batch_size = 4
        self.seq_len = 8
        self.d_model = 64
        
        self.input_1d = torch.randn(self.batch_size, self.d_model)
        self.input_2d = torch.randn(self.batch_size, self.seq_len, self.d_model)
    
    def test_batch_norm_1d(self):
        """Test 1D batch normalization utility."""
        result = batch_norm_1d(self.input_1d)
        
        assert result.shape == self.input_1d.shape
        assert torch.isfinite(result).all()
        
        # Should have approximately zero mean and unit variance across batch
        batch_mean = result.mean(dim=0)
        batch_var = result.var(dim=0, unbiased=False)
        
        torch.testing.assert_close(
            batch_mean, torch.zeros_like(batch_mean), rtol=1e-4, atol=1e-3
        )
        torch.testing.assert_close(
            batch_var, torch.ones_like(batch_var), rtol=1e-3, atol=1e-2
        )
    
    def test_instance_norm(self):
        """Test instance normalization utility."""
        result = instance_norm(self.input_2d)
        
        assert result.shape == self.input_2d.shape
        assert torch.isfinite(result).all()
        
        # Each instance should be normalized independently
        for i in range(self.batch_size):
            instance = result[i]
            instance_mean = instance.mean()
            instance_var = instance.var(unbiased=False)
            
            torch.testing.assert_close(instance_mean, torch.tensor(0.0), rtol=1e-6, atol=1e-5)
            torch.testing.assert_close(instance_var, torch.tensor(1.0), rtol=1e-5, atol=1e-4)
    
    def test_spectral_norm(self):
        """Test spectral normalization utility."""
        # Create a weight matrix
        weight = torch.randn(self.d_model, self.d_model)
        
        normalized_weight = spectral_norm(weight)
        
        assert normalized_weight.shape == weight.shape
        assert torch.isfinite(normalized_weight).all()
        
        # Spectral norm should be approximately 1
        U, S, V = torch.svd(normalized_weight)
        spectral_norm_value = S[0]  # Largest singular value
        
        torch.testing.assert_close(
            spectral_norm_value, torch.tensor(1.0), rtol=0.5, atol=0.5
        )
    
    # test_gradient_normalization removed - not part of DELTA specification
    # DELTA achieves gradient stability through dual-objective learning design,
    # not through utility functions that normalize gradients (see Theorem 2)


class TestNormalizationIntegration:
    """Integration tests for normalization in DELTA context."""
    
    def setup_method(self):
        """Set up test fixtures for integration testing."""
        torch.manual_seed(42)
        self.batch_size = 4
        self.d_model = 128
        self.num_layers = 6
        
        # Simulate DELTA block processing
        self.V_T = torch.randn(self.batch_size, self.d_model)      # Thesis
        self.V_S_raw = torch.randn(self.batch_size, self.d_model)  # Raw synthesis
        self.h_in = torch.randn(self.batch_size, self.d_model)     # Input state
    
    def test_delta_refinement_stage_normalization(self):
        """Test normalization in DELTA Stage 4 refinement."""
        # Stage 4: Refinement and State Update (from specification)
        # h' = LayerNorm(V_T + V_S,raw)
        # V_S = LayerNorm(h' + FFN(h'))
        
        layer_norm1 = LayerNorm(self.d_model)
        layer_norm2 = LayerNorm(self.d_model)
        
        # First normalization
        h_prime = layer_norm1(self.V_T + self.V_S_raw)
        assert h_prime.shape == (self.batch_size, self.d_model)
        assert torch.isfinite(h_prime).all()
        
        # Simulate FFN
        ffn_linear1 = torch.nn.Linear(self.d_model, self.d_model * 4)
        ffn_linear2 = torch.nn.Linear(self.d_model * 4, self.d_model)
        
        ffn_out = ffn_linear2(torch.relu(ffn_linear1(h_prime)))
        
        # Second normalization
        V_S = layer_norm2(h_prime + ffn_out)
        assert V_S.shape == (self.batch_size, self.d_model)
        assert torch.isfinite(V_S).all()
    
    def test_multi_layer_normalization_stability(self):
        """Test normalization stability across multiple layers."""
        layer_norms = [LayerNorm(self.d_model) for _ in range(self.num_layers)]
        
        x = self.h_in
        for i, norm in enumerate(layer_norms):
            x = norm(x)
            
            # Check stability at each layer
            assert torch.isfinite(x).all()
            assert not torch.isnan(x).any()
            
            # Values should remain in reasonable range
            assert x.abs().max() < 100.0  # Reasonable upper bound
    
    def test_normalization_gradient_flow_multi_layer(self):
        """Test gradient flow through multiple normalization layers."""
        layer_norms = [LayerNorm(self.d_model) for _ in range(self.num_layers)]
        
        x = self.h_in.clone().requires_grad_(True)
        
        for norm in layer_norms:
            x = norm(x)
        
        loss = x.sum()
        loss.backward()
        
        # Check that layer parameters received gradients
        for norm in layer_norms:
            assert norm.weight.grad is not None
            assert norm.bias.grad is not None
            assert torch.isfinite(norm.weight.grad).all()
            assert torch.isfinite(norm.bias.grad).all()
        
        # All normalization layers should receive parameter gradients
        for norm in layer_norms:
            assert norm.weight.grad is not None
            assert norm.bias.grad is not None
            assert torch.isfinite(norm.weight.grad).all()
            assert torch.isfinite(norm.bias.grad).all()
    
    def test_normalization_consistency_across_batch_sizes(self):
        """Test normalization consistency across different batch sizes."""
        batch_sizes = [1, 2, 8, 16]
        layer_norm = LayerNorm(self.d_model)
        
        # Use same parameters for all tests
        reference_weight = layer_norm.weight.clone()
        reference_bias = layer_norm.bias.clone()
        
        results = []
        for batch_size in batch_sizes:
            # Reset parameters
            with torch.no_grad():
                layer_norm.weight.copy_(reference_weight)
                layer_norm.bias.copy_(reference_bias)
            
            input_tensor = torch.randn(batch_size, self.d_model)
            result = layer_norm(input_tensor)
            
            assert result.shape == input_tensor.shape
            assert torch.isfinite(result).all()
            
            # Store first element for consistency check
            results.append(result[0] if batch_size > 0 else result)
        
        # Results for same input should be consistent regardless of batch size
        # (This test uses different random inputs, so we just check stability)
        for result in results:
            assert torch.isfinite(result).all()
    
    def test_mixed_normalization_strategies(self):
        """Test mixing different normalization strategies."""
        layer_norm = LayerNorm(self.d_model)
        rms_norm = RMSNorm(self.d_model)
        scale_norm = ScaleNorm(self.d_model)
        
        x = self.h_in
        
        # Apply different normalizations in sequence
        x1 = layer_norm(x)
        x2 = rms_norm(x1)
        x3 = scale_norm(x2)
        
        # Final result should be stable
        assert x3.shape == self.h_in.shape
        assert torch.isfinite(x3).all()
        assert not torch.isnan(x3).any()
    
    def test_normalization_numerical_precision(self):
        """Test normalization numerical precision with extreme values."""
        # Test with values that might cause numerical issues
        extreme_cases = [
            torch.full((self.batch_size, self.d_model), 1e-8),   # Very small
            torch.full((self.batch_size, self.d_model), 1e8),    # Very large
            torch.zeros(self.batch_size, self.d_model),          # All zeros
            torch.ones(self.batch_size, self.d_model) * 1e-10,   # Near zero
        ]
        
        normalizations = [
            LayerNorm(self.d_model),
            RMSNorm(self.d_model),
            ScaleNorm(self.d_model)
        ]
        
        for norm in normalizations:
            for extreme_case in extreme_cases:
                result = norm(extreme_case)
                
                # Should handle extreme cases gracefully
                assert torch.isfinite(result).all()
                assert not torch.isnan(result).any()


if __name__ == "__main__":
    pytest.main([__file__])
