"""Memory optimization utilities for the κ-Cache system.

This module provides tools for compressing, analyzing, and optimizing
memory usage of the κ-Cache during DELTA model operation.
"""

import torch
from torch import Tensor
from typing import Optional, Dict, Tuple, List

from .kappa_cache import KappaCache


class CacheMemoryOptimizer:
    """Optimizer for κ-Cache memory usage and performance.
    
    Provides various strategies for reducing memory footprint while
    maintaining performance characteristics required by the DELTA architecture.
    """
    
    def __init__(self, compression_threshold: float = 0.8, precision_threshold: float = 1e-6):
        """Initialize memory optimizer.
        
        Args:
            compression_threshold: Fraction of variance to preserve in compression
            precision_threshold: Minimum precision for quantization
        """
        self.compression_threshold = compression_threshold
        self.precision_threshold = precision_threshold
    
    def compress_cache(self, cache: KappaCache, method: str = "svd") -> KappaCache:
        """Compress κ-Cache using specified method.
        
        Args:
            cache: Original κ-Cache to compress
            method: Compression method ("svd", "quantize", "sparsify")
            
        Returns:
            Compressed κ-Cache with reduced memory footprint
            
        Raises:
            ValueError: If compression method is not supported
        """
        if method == "svd":
            return self._compress_svd(cache)
        elif method == "quantize":
            return self._compress_quantize(cache)
        elif method == "sparsify":
            return self._compress_sparsify(cache)
        else:
            raise ValueError(f"Unsupported compression method: {method}")
    
    def _compress_svd(self, cache: KappaCache) -> KappaCache:
        """Compress using Singular Value Decomposition.
        
        Reduces dimensionality while preserving most variance.
        """
        compressed_cache = KappaCache(
            num_layers=cache.num_layers,
            d_model=cache.d_model,
            max_seq_length=cache.max_seq_length,
            dtype=cache.dtype
        )
        
        # Initialize compressed cache with same dimensions
        compressed_cache.initialize(cache.batch_size, cache.seq_length, cache.device)
        
        for layer_idx in range(cache.num_layers):
            layer_cache = cache.get_cache_for_layer(layer_idx)
            
            # Reshape for SVD: (batch * seq, d_model)
            original_shape = layer_cache.shape
            reshaped = layer_cache.view(-1, cache.d_model)
            
            # Perform SVD
            U, S, Vt = torch.linalg.svd(reshaped, full_matrices=False)
            
            # Determine rank to preserve threshold of variance
            total_variance = torch.sum(S ** 2)
            cumsum_variance = torch.cumsum(S ** 2, dim=0)
            preserved_ratio = cumsum_variance / total_variance
            
            # Find minimum rank to preserve threshold
            rank = torch.argmax((preserved_ratio >= self.compression_threshold).float()) + 1
            rank = max(1, min(rank.item(), S.shape[0]))
            
            # Truncate to selected rank
            U_trunc = U[:, :rank]
            S_trunc = S[:rank]
            Vt_trunc = Vt[:rank, :]
            
            # Reconstruct compressed tensor
            compressed = torch.mm(U_trunc * S_trunc.unsqueeze(0), Vt_trunc)
            compressed = compressed.view(original_shape)
            
            compressed_cache.set_layer_cache(layer_idx, compressed)
        
        return compressed_cache
    
    def _compress_quantize(self, cache: KappaCache) -> KappaCache:
        """Compress using quantization to lower precision.
        
        Reduces memory by using fewer bits per value.
        """
        compressed_cache = KappaCache(
            num_layers=cache.num_layers,
            d_model=cache.d_model,
            max_seq_length=cache.max_seq_length,
            dtype=torch.float16  # Use half precision
        )
        
        # Initialize compressed cache with same dimensions
        compressed_cache.initialize(cache.batch_size, cache.seq_length, cache.device)
        
        for layer_idx in range(cache.num_layers):
            layer_cache = cache.get_cache_for_layer(layer_idx)
            # Quantize to half precision
            quantized = layer_cache.half()
            compressed_cache.set_layer_cache(layer_idx, quantized)
        
        return compressed_cache
    
    def _compress_sparsify(self, cache: KappaCache) -> KappaCache:
        """Compress by sparsifying small values.
        
        Sets values below threshold to zero to enable sparse storage.
        """
        compressed_cache = KappaCache(
            num_layers=cache.num_layers,
            d_model=cache.d_model,
            max_seq_length=cache.max_seq_length,
            dtype=cache.dtype
        )
        
        # Initialize compressed cache with same dimensions
        compressed_cache.initialize(cache.batch_size, cache.seq_length, cache.device)
        
        for layer_idx in range(cache.num_layers):
            layer_cache = cache.get_cache_for_layer(layer_idx)
            
            # Calculate sparsification threshold
            abs_values = torch.abs(layer_cache)
            threshold = torch.quantile(abs_values, 1.0 - self.compression_threshold)
            
            # Sparsify by setting small values to zero
            mask = abs_values >= threshold
            sparsified = layer_cache * mask
            
            compressed_cache.set_layer_cache(layer_idx, sparsified)
        
        return compressed_cache
    
    def estimate_memory_usage(self, cache: KappaCache) -> int:
        """Estimate memory usage of κ-Cache in bytes.
        
        Args:
            cache: κ-Cache to analyze
            
        Returns:
            Estimated memory usage in bytes
        """
        if cache.is_empty:
            return 0
        
        total_bytes = 0
        for layer_idx in range(cache.num_layers):
            if layer_idx in cache.cache:
                layer_cache = cache.cache[layer_idx]
                # Calculate tensor memory usage
                numel = layer_cache.numel()
                bytes_per_element = layer_cache.element_size()
                total_bytes += numel * bytes_per_element
        
        # Add overhead for dictionary and metadata
        overhead = cache.num_layers * 64  # Approximate dict overhead per entry
        total_bytes += overhead
        
        return total_bytes
    
    def analyze_cache_statistics(self, cache: KappaCache) -> Dict[str, float]:
        """Analyze statistical properties of κ-Cache.
        
        Args:
            cache: κ-Cache to analyze
            
        Returns:
            Dictionary containing various statistics
        """
        if cache.is_empty:
            return {"error": "Cache is empty"}
        
        stats = {}
        all_values = []
        
        # Collect statistics from all layers
        for layer_idx in range(cache.num_layers):
            if layer_idx in cache.cache:
                layer_cache = cache.cache[layer_idx]
                values = layer_cache.flatten()
                all_values.append(values)
                
                # Per-layer statistics
                stats[f"layer_{layer_idx}_mean"] = torch.mean(values).item()
                stats[f"layer_{layer_idx}_std"] = torch.std(values).item()
                stats[f"layer_{layer_idx}_min"] = torch.min(values).item()
                stats[f"layer_{layer_idx}_max"] = torch.max(values).item()
                stats[f"layer_{layer_idx}_sparsity"] = (values == 0).float().mean().item()
        
        # Global statistics
        if all_values:
            global_values = torch.cat(all_values)
            stats["global_mean"] = torch.mean(global_values).item()
            stats["global_std"] = torch.std(global_values).item()
            stats["global_min"] = torch.min(global_values).item()
            stats["global_max"] = torch.max(global_values).item()
            stats["global_sparsity"] = (global_values == 0).float().mean().item()
            stats["memory_mb"] = self.estimate_memory_usage(cache) / (1024 ** 2)
        
        return stats
    
    def suggest_optimization(self, cache: KappaCache) -> Dict[str, str]:
        """Suggest optimization strategies based on cache analysis.
        
        Args:
            cache: κ-Cache to analyze
            
        Returns:
            Dictionary of optimization suggestions
        """
        stats = self.analyze_cache_statistics(cache)
        suggestions = {}
        
        if "global_sparsity" in stats:
            sparsity = stats["global_sparsity"]
            if sparsity > 0.5:
                suggestions["sparsification"] = f"High sparsity ({sparsity:.1%}) - consider sparse storage"
            
            memory_mb = stats.get("memory_mb", 0)
            if memory_mb > 1000:  # > 1GB
                suggestions["compression"] = f"Large memory usage ({memory_mb:.1f}MB) - consider SVD compression"
            
            if cache.dtype == torch.float32:
                suggestions["quantization"] = "Using float32 - consider quantization to float16"
        
        return suggestions
    
    def optimize_automatically(self, cache: KappaCache) -> Tuple[KappaCache, Dict[str, str]]:
        """Automatically optimize cache based on analysis.
        
        Args:
            cache: Original κ-Cache
            
        Returns:
            Tuple of (optimized_cache, applied_optimizations)
        """
        suggestions = self.suggest_optimization(cache)
        applied = {}
        optimized_cache = cache
        
        # Apply optimizations in order of impact
        if "quantization" in suggestions:
            optimized_cache = self._compress_quantize(optimized_cache)
            applied["quantization"] = "Applied float16 quantization"
        
        if "sparsification" in suggestions:
            optimized_cache = self._compress_sparsify(optimized_cache)
            applied["sparsification"] = "Applied sparsification"
        
        if "compression" in suggestions:
            optimized_cache = self._compress_svd(optimized_cache)
            applied["compression"] = "Applied SVD compression"
        
        return optimized_cache, applied
