# DELTA Decoder Implementation

This directory contains a complete implementation of the DELTA (Dialectic Evaluative Layered Tension Attractor) decoder architecture as described in the paper.

## Architecture Overview

DELTA is a novel decoder architecture that replaces traditional self-attention with a dialectic process:
- **Thesis**: Initial proposal via learned projection
- **Antithesis**: Constraint vector from structural and semantic attractors  
- **Synthesis**: FFT-based resolution of the dialectic tension
- **Refinement**: Integration into state via residual connections and gating

## Key Components

### Core Architecture (`delta_block/`)
- `thesis.py`: Linear projection for initial proposals
- `antithesis.py`: Constraint generation via attractor banks
- `synthesis.py`: FFT-based circular convolution
- `refinement.py`: Residual FFN and GRU-like state updates
- `gating.py`: Effective antithesis computation during decoding

### Attractors (`attractors/`)
- `base.py`: Abstract base classes for attractors
- `structural.py`: Stateless attractors for syntactic/logical rules
- `semantic.py`: Stateful GRU-based attractors for context tracking

### Three-Phase Operation (`phases/`)
- **Prefill**: Sequential processing to generate κ-cache
- **Decoding**: Autoregressive generation with κ-cache attention
- **Mutation**: Essence extraction and horizon adaptation

### Caching (`cache/`)
- `kappa_cache.py`: Efficient storage and retrieval of antithesis vectors

### Training (`training/`)
- `dual_objective.py`: Dual-objective gradient learning system
  - Thesis components trained with local tension loss
  - Antithesis components trained with global task loss

### Models (`models/`)
- `delta_model.py`: Complete DELTA model with embeddings and three-phase cycle

## Key Features

1. **Linear Complexity**: O(L) prefill, O(L) per-token decoding (where L is prompt length)
2. **Dialectic Reasoning**: Structured conflict resolution at each layer
3. **Stateful Context**: Semantic attractors maintain context across time
4. **Adaptive Learning**: Mutation phase for inference-time adaptation
5. **Specialized Components**: Dual-objective training for functional specialization

## Usage

```python
from source.models import DELTAModel, DELTAConfig

# Configure model
config = DELTAConfig(
    d_model=512,
    n_layers=12,
    n_attractors=8,
    semantic_hidden_size=256
)

# Initialize
model = DELTAModel(config)

# Generate text
generated_ids, details = model.generate(
    prompt_ids,
    max_length=100,
    do_mutation=True
)
```

See `examples/basic_usage.py` for complete examples.