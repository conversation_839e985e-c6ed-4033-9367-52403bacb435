"""Dual-Objective Learning Implementation

This module implements the dual-objective learning system described in Section 5 of 
the DELTA specification. It separates parameter updates between thesis (proposal) 
and antithesis (control) components using different loss functions.
"""

import torch
import torch.nn as nn
from typing import Dict, List, Tuple, Optional, Any
import logging

from ..core.base.types import DELTAConfig, TrainingMetrics
from ..learning.local_loss import compute_local_tension_loss
from ..learning.global_loss import compute_global_task_loss
from ..math.advanced.gradient_blocking import detach_tensor, dual_objective_update
from ..utils.constants import (
    THESIS_COMPONENTS, 
    ANTITHESIS_COMPONENTS, 
    CONTROL_COMPONENTS,
    GLOBAL_LOSS_WEIGHT,
    LOCAL_LOSS_WEIGHT
)

logger = logging.getLogger(__name__)


class DualObjectiveLearner:
    """
    Implements dual-objective learning with separated gradient flows.
    
    The system uses two distinct loss functions:
    1. Global Task Loss (L_global): Applied to antithesis/control components
    2. Local Tension Loss (L_local): Applied to thesis components
    
    This separation encourages functional specialization where thesis components
    learn to make better proposals while antithesis components learn optimal constraints.
    """
    
    def __init__(
        self,
        config: DELTAConfig,
        model: nn.<PERSON><PERSON><PERSON>,
        thesis_optimizer: Optional[torch.optim.Optimizer] = None,
        antithesis_optimizer: Optional[torch.optim.Optimizer] = None
    ):
        """Initialize dual-objective learner.
        
        Args:
            config: DELTA model configuration
            model: The DELTA model to train
            thesis_optimizer: Optimizer for thesis parameters (local loss)
            antithesis_optimizer: Optimizer for antithesis parameters (global loss)
        """
        self.config = config
        self.model = model
        self.logger = logger
        
        # Separate parameter groups
        self.thesis_params, self.antithesis_params = self._separate_parameters()
        
        # Initialize optimizers if not provided
        self.thesis_optimizer = thesis_optimizer or self._create_thesis_optimizer()
        self.antithesis_optimizer = antithesis_optimizer or self._create_antithesis_optimizer()
        
        # Loss weights
        self.global_loss_weight = GLOBAL_LOSS_WEIGHT
        self.local_loss_weight = LOCAL_LOSS_WEIGHT
        
        # Training metrics tracking
        self.step_count = 0
        self.metrics_history: List[TrainingMetrics] = []
        
        logger.info(f"Initialized dual-objective learning:")
        logger.info(f"  Thesis parameters: {len(self.thesis_params)}")
        logger.info(f"  Antithesis parameters: {len(self.antithesis_params)}")
    
    def _separate_parameters(self) -> Tuple[List[nn.Parameter], List[nn.Parameter]]:
        """Separate model parameters into thesis and antithesis groups using explicit methods.
        
        This approach is safer than regex-based detection as it uses the explicit
        get_local_loss_parameters() and get_global_loss_parameters() methods
        defined by DELTA components.
        
        Returns:
            Tuple of (thesis_parameters, antithesis_parameters)
        """
        thesis_params = []
        antithesis_params = []
        
        # Collect parameters from DELTA blocks using explicit methods
        if hasattr(self.model, 'stack') and hasattr(self.model.stack, 'layers'):
            # Model has a stack of DELTA blocks
            for layer in self.model.stack.layers:
                if hasattr(layer, 'get_local_loss_parameters'):
                    thesis_params.extend(layer.get_local_loss_parameters())
                if hasattr(layer, 'get_global_loss_parameters'):
                    antithesis_params.extend(layer.get_global_loss_parameters())
        
        # Also check for direct mix-in usage in modules
        for module in self.model.modules():
            # Check for mix-in methods
            if hasattr(module, 'get_thesis_parameters'):
                thesis_params.extend(module.get_thesis_parameters())
            if hasattr(module, 'get_antithesis_parameters'):
                antithesis_params.extend(module.get_antithesis_parameters())
            if hasattr(module, 'get_control_parameters'):
                antithesis_params.extend(module.get_control_parameters())
        
        # Remove duplicates while preserving parameter identity
        thesis_param_ids = {id(p) for p in thesis_params}
        antithesis_param_ids = {id(p) for p in antithesis_params}
        
        # Deduplicate thesis parameters
        thesis_params = [p for p in thesis_params if id(p) in thesis_param_ids]
        thesis_param_ids = {id(p) for p in thesis_params}  # Rebuild after dedup
        
        # Deduplicate antithesis parameters and remove overlap with thesis
        antithesis_params = [p for p in antithesis_params 
                           if id(p) in antithesis_param_ids and id(p) not in thesis_param_ids]
        
        # Fallback: if explicit methods not available, use regex-based detection
        if not thesis_params and not antithesis_params:
            logger.warning("Explicit parameter methods not found, falling back to regex-based detection")
            return self._separate_parameters_regex()
        
        # Handle other model parameters (embeddings, output layers, etc.)
        collected_param_ids = {id(p) for p in thesis_params + antithesis_params}
        
        for name, param in self.model.named_parameters():
            if id(param) not in collected_param_ids:
                # Default: assign uncategorized parameters to antithesis (control) group
                antithesis_params.append(param)
                logger.debug(f"Parameter '{name}' assigned to antithesis group (default)")
        
        logger.info(f"Parameter separation: {len(thesis_params)} thesis, {len(antithesis_params)} antithesis")
        return thesis_params, antithesis_params
    
    def _separate_parameters_regex(self) -> Tuple[List[nn.Parameter], List[nn.Parameter]]:
        """Fallback regex-based parameter separation (legacy method)."""
        thesis_params = []
        antithesis_params = []
        
        for name, param in self.model.named_parameters():
            if self._is_thesis_parameter(name):
                thesis_params.append(param)
            elif self._is_antithesis_parameter(name):
                antithesis_params.append(param)
            else:
                # Default: assign to antithesis (control) group
                antithesis_params.append(param)
                logger.debug(f"Parameter '{name}' assigned to antithesis group (default)")
        
        return thesis_params, antithesis_params
    
    def _is_thesis_parameter(self, param_name: str) -> bool:
        """Check if a parameter belongs to thesis components."""
        return any(component in param_name for component in THESIS_COMPONENTS)
    
    def _is_antithesis_parameter(self, param_name: str) -> bool:
        """Check if a parameter belongs to antithesis/control components."""
        return any(component in param_name for component in 
                  ANTITHESIS_COMPONENTS + CONTROL_COMPONENTS)
    
    def _create_thesis_optimizer(self) -> torch.optim.Optimizer:
        """Create optimizer for thesis parameters."""
        return torch.optim.AdamW(
            self.thesis_params,
            lr=self.config.thesis_lr,
            weight_decay=0.01,
            betas=(0.9, 0.999)
        )
    
    def _create_antithesis_optimizer(self) -> torch.optim.Optimizer:
        """Create optimizer for antithesis parameters."""
        return torch.optim.AdamW(
            self.antithesis_params,
            lr=self.config.antithesis_lr,
            weight_decay=0.01,
            betas=(0.9, 0.999)
        )
    
    def compute_losses(
        self,
        model_outputs: List[Any],  # List of DELTABlockOutput from each layer
        targets: torch.Tensor,
        input_ids: Optional[torch.Tensor] = None
    ) -> Tuple[torch.Tensor, List[torch.Tensor]]:
        """Compute global and local losses for dual-objective learning.
        
        Args:
            model_outputs: List of outputs from each DELTA block
            targets: Target tokens for global loss computation
            input_ids: Input token IDs (optional, for additional context)
            
        Returns:
            Tuple of (global_loss, list_of_local_losses_per_layer)
        """
        # 1. Compute Global Task Loss (for antithesis/control parameters)
        # Use the final model output for global loss
        if hasattr(model_outputs[-1], 'output_state'):
            final_output = model_outputs[-1].output_state
        else:
            final_output = model_outputs[-1]
            
        global_loss = compute_global_task_loss(final_output, targets)
        
        # 2. Compute Local Tension Losses (for thesis parameters)
        local_losses = []
        
        for layer_idx, layer_output in enumerate(model_outputs):
            if hasattr(layer_output, 'thesis') and hasattr(layer_output, 'kappa_effective'):
                # Use effective antithesis if available (during decoding)
                antithesis = layer_output.kappa_effective
                if antithesis is None:
                    # Fall back to local antithesis (during prefill)
                    antithesis = layer_output.local_antithesis
                
                # Detach antithesis to block gradients (as per spec)
                antithesis_detached = detach_tensor(antithesis)
                
                # Compute local tension loss for this layer
                local_loss = compute_local_tension_loss(
                    thesis=layer_output.thesis,
                    effective_antithesis=antithesis_detached
                )
                local_losses.append(local_loss)
            else:
                # If layer doesn't have thesis/antithesis outputs, use zero loss
                device = global_loss.device
                local_losses.append(torch.tensor(0.0, device=device, requires_grad=True))
        
        return global_loss, local_losses
    
    def training_step(
        self,
        model_outputs: List[Any],
        targets: torch.Tensor,
        input_ids: Optional[torch.Tensor] = None
    ) -> TrainingMetrics:
        """Perform one training step with dual-objective learning.
        
        Args:
            model_outputs: List of outputs from each DELTA block
            targets: Target tokens
            input_ids: Input token IDs (optional)
            
        Returns:
            TrainingMetrics with loss values and gradient norms
        """
        # Zero gradients
        self.thesis_optimizer.zero_grad()
        self.antithesis_optimizer.zero_grad()
        
        # Compute losses
        global_loss, local_losses = self.compute_losses(model_outputs, targets, input_ids)
        
        # Apply loss weights
        weighted_global_loss = self.global_loss_weight * global_loss
        weighted_local_losses = [self.local_loss_weight * loss for loss in local_losses]
        
        # Backward pass for global loss (antithesis/control parameters)
        weighted_global_loss.backward(retain_graph=True)
        
        # Backward pass for local losses (thesis parameters)
        total_local_loss = sum(weighted_local_losses)
        if total_local_loss.requires_grad:
            total_local_loss.backward()
        
        # Compute gradient norms before clipping
        thesis_grad_norm = self._compute_grad_norm(self.thesis_params)
        antithesis_grad_norm = self._compute_grad_norm(self.antithesis_params)
        
        # Gradient clipping
        if hasattr(self.config, 'gradient_clipping') and self.config.gradient_clipping > 0:
            torch.nn.utils.clip_grad_norm_(self.thesis_params, self.config.gradient_clipping)
            torch.nn.utils.clip_grad_norm_(self.antithesis_params, self.config.gradient_clipping)
        
        # Optimizer steps
        self.thesis_optimizer.step()
        self.antithesis_optimizer.step()
        
        # Update step count
        self.step_count += 1
        
        # Create training metrics
        metrics = TrainingMetrics(
            global_loss=global_loss.item(),
            local_losses=[loss.item() for loss in local_losses],
            thesis_grad_norm=thesis_grad_norm,
            antithesis_grad_norm=antithesis_grad_norm
        )
        
        self.metrics_history.append(metrics)
        
        # Log metrics periodically
        if self.step_count % 100 == 0:
            self.logger.info(
                f"Step {self.step_count}: Global={global_loss.item():.4f}, "
                f"Local={total_local_loss.item():.4f}, "
                f"Thesis_grad={thesis_grad_norm:.4f}, "
                f"Antithesis_grad={antithesis_grad_norm:.4f}"
            )
        
        return metrics
    
    def _compute_grad_norm(self, parameters: List[nn.Parameter]) -> float:
        """Compute the L2 norm of gradients for a parameter group."""
        total_norm = 0.0
        for param in parameters:
            if param.grad is not None:
                param_norm = param.grad.data.norm(2)
                total_norm += param_norm.item() ** 2
        return total_norm ** 0.5
    
    def get_learning_rates(self) -> Dict[str, float]:
        """Get current learning rates for both optimizers."""
        return {
            'thesis_lr': self.thesis_optimizer.param_groups[0]['lr'],
            'antithesis_lr': self.antithesis_optimizer.param_groups[0]['lr']
        }
    
    def set_learning_rates(self, thesis_lr: Optional[float] = None, antithesis_lr: Optional[float] = None):
        """Update learning rates for optimizers."""
        if thesis_lr is not None:
            for param_group in self.thesis_optimizer.param_groups:
                param_group['lr'] = thesis_lr
                
        if antithesis_lr is not None:
            for param_group in self.antithesis_optimizer.param_groups:
                param_group['lr'] = antithesis_lr
    
    def get_metrics_summary(self, last_n_steps: int = 100) -> Dict[str, float]:
        """Get summary statistics for recent training metrics."""
        if not self.metrics_history:
            return {}
        
        recent_metrics = self.metrics_history[-last_n_steps:]
        
        global_losses = [m.global_loss for m in recent_metrics]
        thesis_grads = [m.thesis_grad_norm for m in recent_metrics]
        antithesis_grads = [m.antithesis_grad_norm for m in recent_metrics]
        
        return {
            'avg_global_loss': sum(global_losses) / len(global_losses),
            'avg_thesis_grad_norm': sum(thesis_grads) / len(thesis_grads),
            'avg_antithesis_grad_norm': sum(antithesis_grads) / len(antithesis_grads),
            'total_steps': self.step_count,
            'recent_steps': len(recent_metrics)
        }
    
    def save_state(self) -> Dict[str, Any]:
        """Save the state of the dual-objective learner."""
        return {
            'step_count': self.step_count,
            'thesis_optimizer': self.thesis_optimizer.state_dict(),
            'antithesis_optimizer': self.antithesis_optimizer.state_dict(),
            'metrics_history': self.metrics_history[-1000:],  # Keep last 1000 metrics
            'config': {
                'thesis_lr': self.config.thesis_lr,
                'antithesis_lr': self.config.antithesis_lr,
                'global_loss_weight': self.global_loss_weight,
                'local_loss_weight': self.local_loss_weight
            }
        }
    
    def load_state(self, state_dict: Dict[str, Any]):
        """Load the state of the dual-objective learner."""
        self.step_count = state_dict.get('step_count', 0)
        
        if 'thesis_optimizer' in state_dict:
            self.thesis_optimizer.load_state_dict(state_dict['thesis_optimizer'])
            
        if 'antithesis_optimizer' in state_dict:
            self.antithesis_optimizer.load_state_dict(state_dict['antithesis_optimizer'])
            
        if 'metrics_history' in state_dict:
            self.metrics_history = state_dict['metrics_history']
            
        if 'config' in state_dict:
            config = state_dict['config']
            self.global_loss_weight = config.get('global_loss_weight', GLOBAL_LOSS_WEIGHT)
            self.local_loss_weight = config.get('local_loss_weight', LOCAL_LOSS_WEIGHT)
