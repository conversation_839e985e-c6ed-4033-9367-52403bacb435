# Build automation for DELTA project
.PHONY: help install install-dev test lint format clean docs docker

help:
	@echo "Available commands:"
	@echo "  install     Install package and dependencies"
	@echo "  install-dev Install package with development dependencies"
	@echo "  test        Run test suite"
	@echo "  lint        Run linting (flake8, mypy)"
	@echo "  format      Format code (black, isort)"
	@echo "  clean       Clean build artifacts"
	@echo "  docs        Build documentation"
	@echo "  docker      Build Docker image"

install:
	pip install -e .

install-dev:
	pip install -e ".[dev,experiment]"

test:
	pytest tests/ -v

lint:
	flake8 source/ data/ scripts/ tests/
	mypy source/ data/ scripts/

format:
	black source/ data/ scripts/ tests/
	isort source/ data/ scripts/ tests/

clean:
	rm -rf build/
	rm -rf dist/
	rm -rf *.egg-info/
	find . -type d -name __pycache__ -delete
	find . -type f -name "*.pyc" -delete

docs:
	cd docs && make html

docker:
	docker build -t delta-decoder:latest .

# Training shortcuts
train-small:
	python scripts/train.py --config configs/small_model.yaml

train-base:
	python scripts/train.py --config configs/base_model.yaml

# Evaluation shortcuts
eval-reasoning:
	python scripts/evaluate.py --task reasoning --model-path checkpoints/latest.pt

eval-constraints:
	python scripts/evaluate.py --task constraints --model-path checkpoints/latest.pt
