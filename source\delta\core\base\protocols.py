"""Python protocols/interfaces for DELTA components.

This module defines formal interface definitions using Python's Protocol
feature to ensure consistent implementation across DELTA components.
"""

from typing import Protocol, Optional, Tuple, List, Dict
from abc import abstractmethod
import torch
from torch import Tensor

from .types import (
    StateVector, AttractorStates, AttractorOutput, 
    DELTABlockOutput, KappaCache, DELTAConfig, LayerIndex
)


class Attractor(Protocol):
    """Base protocol for all attractor types."""
    
    @abstractmethod
    def forward(self, input_vector: Tensor, hidden_state: Optional[Tensor] = None) -> AttractorOutput:
        """Compute attractor force given input.
        
        Args:
            input_vector: Input to the attractor (e.g., thesis or summary vector)
            hidden_state: Optional hidden state for stateful attractors
            
        Returns:
            AttractorOutput containing force and optional updated hidden state
        """
        ...
    
    @abstractmethod
    def reset_state(self, batch_size: int) -> Optional[Tensor]:
        """Reset internal state for stateful attractors.
        
        Args:
            batch_size: Batch size for state initialization
            
        Returns:
            Initial hidden state or None for stateless attractors
        """
        ...


class StructuralAttractor(Attractor):
    """Protocol for stateless structural attractors."""
    
    @abstractmethod
    def forward(self, thesis: Tensor, hidden_state: Optional[Tensor] = None) -> AttractorOutput:
        """Compute structural constraint force.
        
        Structural attractors are stateless and compute forces based on
        learned transformations of the thesis.
        """
        ...


class SemanticAttractor(Attractor):
    """Protocol for stateful semantic attractors."""
    
    @abstractmethod
    def forward(self, summary: Tensor, hidden_state: Tensor) -> AttractorOutput:
        """Compute semantic constraint force with state update.
        
        Semantic attractors maintain hidden state across time steps
        to track contextual information.
        """
        ...


class GatingNetwork(Protocol):
    """Protocol for gating networks that weight attractor contributions."""
    
    @abstractmethod
    def forward(self, summary_vector: Tensor) -> Tensor:
        """Compute gating weights for attractors.
        
        Args:
            summary_vector: Summary of the thesis
            
        Returns:
            Softmax-normalized weights for each attractor
        """
        ...


class RecurrentCell(Protocol):
    """Protocol for recurrent cells used in semantic attractors.
    
    As specified in DeltaDecoder.md, semantic attractors use recurrent cells
    M_{Sem,i} that update their hidden states based on the summary vector.
    """
    
    @abstractmethod
    def forward(self, input: Tensor, hidden: Tensor) -> Tensor:
        """Update recurrent hidden state.
        
        Args:
            input: Input vector (summary vector v_summary from thesis)
            hidden: Previous hidden state h_{sem,in_i}
            
        Returns:
            Updated hidden state h_{sem,out_i}
        """
        ...
    
    @abstractmethod
    def init_hidden(self, batch_size: int) -> Tensor:
        """Initialize hidden state.
        
        Args:
            batch_size: Batch size for initialization
            
        Returns:
            Initial hidden state tensor
        """
        ...


class TokenEmbedding(Protocol):
    """Protocol for token embedding layer.
    
    Converts token IDs to dense embeddings x_j as specified in the 
    prefill phase: h_{in,j,1} = x_j + p_j
    """
    
    @abstractmethod
    def forward(self, input_ids: Tensor) -> Tensor:
        """Convert token IDs to embeddings.
        
        Args:
            input_ids: Token IDs of shape (batch_size, seq_len)
            
        Returns:
            Token embeddings of shape (batch_size, seq_len, d_model)
        """
        ...


class PositionalEncoding(Protocol):
    """Protocol for positional encoding generation.
    
    Generates positional encodings p_j that are added to token embeddings
    as specified: h_{in,j,1} = x_j + p_j
    """
    
    @abstractmethod
    def forward(self, seq_length: int, d_model: int, device: Optional[torch.device] = None) -> Tensor:
        """Generate positional encodings.
        
        Args:
            seq_length: Sequence length L
            d_model: Model dimension
            device: Target device for the encodings
            
        Returns:
            Positional encodings of shape (1, seq_length, d_model)
        """
        ...


class FeedForwardNetwork(Protocol):
    """Protocol for FFN in refinement stage (Stage 4).
    
    Implements the feed-forward network used in refinement:
    V_S = LayerNorm(h' + FFN(h'))
    """
    
    @abstractmethod
    def forward(self, x: Tensor) -> Tensor:
        """Apply feed-forward transformation.
        
        Args:
            x: Input tensor of shape (batch_size, d_model)
            
        Returns:
            Output tensor of same shape after FFN transformation
        """
        ...


class ThesisGenerator(Protocol):
    """Protocol for thesis generation (Stage 1)."""
    
    @abstractmethod
    def forward(self, input_state: StateVector) -> StateVector:
        """Generate thesis from input state.
        
        Args:
            input_state: Input hidden state
            
        Returns:
            Thesis vector V_T
        """
        ...


class AntitheisGenerator(Protocol):
    """Protocol for antithesis generation (Stage 2)."""
    
    @abstractmethod
    def forward(
        self, 
        thesis: StateVector,
        semantic_states: AttractorStates,
        kappa_cache: Optional[KappaCache] = None,
        layer_idx: Optional[int] = None
    ) -> Tuple[StateVector, AttractorStates]:
        """Generate antithesis from thesis and attractor states.
        
        Args:
            thesis: Thesis vector V_T
            semantic_states: Current hidden states of semantic attractors
            kappa_cache: Optional cache for decoding phase
            layer_idx: Layer index for cache retrieval
            
        Returns:
            Tuple of (antithesis vector κ, updated semantic states)
        """
        ...


class KappaCacheAttention(Protocol):
    """Protocol for attention mechanism over κ-Cache.
    
    Implements the attention mechanism described in the decoding phase:
    α = softmax(K_i V_{T,new,i} / sqrt(d))
    κ_{context,i} = α^T K_i
    """
    
    @abstractmethod
    def forward(
        self, 
        query: Tensor, 
        kappa_cache: Tensor,
        mask: Optional[Tensor] = None
    ) -> Tuple[Tensor, Tensor]:
        """Compute attention over cached κ vectors.
        
        Args:
            query: New thesis V_{T,new,i} of shape (batch_size, d_model)
            kappa_cache: Cached κ vectors K_i of shape (batch_size, prompt_len, d_model)
            mask: Optional attention mask
            
        Returns:
            Tuple of:
                - κ_context: Context vector from cache (batch_size, d_model)
                - attention_weights: Attention weights α (batch_size, prompt_len)
        """
        ...


class SynthesisModule(Protocol):
    """Protocol for frequency-domain synthesis (Stage 3)."""
    
    @abstractmethod
    def forward(self, thesis: StateVector, antithesis: StateVector) -> StateVector:
        """Perform frequency-domain synthesis via FFT convolution.
        
        Args:
            thesis: Thesis vector V_T
            antithesis: Antithesis vector κ
            
        Returns:
            Raw synthesis vector V_S,raw
        """
        ...


class RefinementModule(Protocol):
    """Protocol for refinement and state update (Stage 4)."""
    
    @abstractmethod
    def forward(
        self, 
        input_state: StateVector,
        thesis: StateVector,
        synthesis_raw: StateVector
    ) -> Tuple[StateVector, StateVector]:
        """Refine synthesis and update state.
        
        Args:
            input_state: Original input state h_in
            thesis: Thesis vector V_T
            synthesis_raw: Raw synthesis V_S,raw
            
        Returns:
            Tuple of (output state h_out, refined synthesis V_S)
        """
        ...


class DELTABlock(Protocol):
    """Protocol for a complete DELTA block."""
    
    @abstractmethod
    def forward(
        self,
        input_state: StateVector,
        semantic_states: AttractorStates,
        kappa_cache: Optional[KappaCache] = None,
        layer_idx: Optional[int] = None,
        return_details: bool = False
    ) -> DELTABlockOutput:
        """Forward pass through DELTA block.
        
        Args:
            input_state: Input hidden state
            semantic_states: Hidden states for semantic attractors
            kappa_cache: Optional cache for decoding phase
            layer_idx: Layer index for this block
            return_details: Whether to return detailed intermediate outputs
            
        Returns:
            DELTABlockOutput with results and optional details
        """
        ...


class DELTAStack(Protocol):
    """Protocol for vertical stack of N DELTA blocks.
    
    Implements the vertical processing described in Section 3.2:
    The output of block i becomes the input for block i+1.
    """
    
    @abstractmethod
    def forward(
        self,
        input_state: StateVector,
        initial_semantic_states: Optional[Dict[int, AttractorStates]] = None,
        kappa_cache: Optional[KappaCache] = None,
        return_layer_outputs: bool = False
    ) -> Tuple[StateVector, Dict[int, AttractorStates], Optional[List[DELTABlockOutput]]]:
        """Process input through N DELTA blocks vertically.
        
        Args:
            input_state: Initial input state h_{in,1}
            initial_semantic_states: Initial semantic states for all layers
            kappa_cache: Optional κ-cache for decoding phase
            return_layer_outputs: Whether to return outputs from each layer
            
        Returns:
            Tuple of:
                - final_state: Output from the last block h_{out,N}
                - final_semantic_states: Updated semantic states for all layers
                - layer_outputs: Optional list of outputs from each layer
        """
        ...


class OperationalPhase(Protocol):
    """Protocol for operational phases (Prefill, Decode, Mutate)."""
    
    @abstractmethod
    def execute(self, *args, **kwargs):
        """Execute the operational phase."""
        ...


class PrefillPhase(OperationalPhase):
    """Protocol for prefill phase."""
    
    @abstractmethod
    def execute(
        self,
        prompt_tokens: Tensor,
        model: 'DELTAModel'
    ) -> Tuple[StateVector, KappaCache, Dict[int, AttractorStates]]:
        """Execute prefill phase over prompt sequence.
        
        Args:
            prompt_tokens: Input token IDs
            model: DELTA model instance
            
        Returns:
            Tuple of (final state, populated κ-cache, final semantic states per layer)
        """
        ...


class DecodePhase(OperationalPhase):
    """Protocol for decode phase."""
    
    @abstractmethod
    def execute(
        self,
        initial_state: StateVector,
        initial_semantic_states: Dict[int, AttractorStates],
        kappa_cache: KappaCache,
        model: 'DELTAModel',
        max_length: int,
        temperature: float = 1.0
    ) -> List[int]:
        """Execute autoregressive decoding.
        
        Args:
            initial_state: Initial state from prefill
            initial_semantic_states: Initial semantic states per layer
            kappa_cache: Populated cache from prefill
            model: DELTA model instance
            max_length: Maximum generation length
            temperature: Sampling temperature
            
        Returns:
            List of generated token IDs
        """
        ...


class MutatePhase(OperationalPhase):
    """Protocol for mutation phase."""
    
    @abstractmethod
    def execute(
        self,
        synthesis_history: List[StateVector],
        semantic_states: Dict[int, AttractorStates],
        success_score: float,
        config: DELTAConfig
    ) -> Dict[int, AttractorStates]:
        """Execute mutation phase to adapt model state.
        
        Args:
            synthesis_history: History of synthesis states from successful generation
            semantic_states: Current semantic attractor states per layer
            success_score: Score indicating generation success
            config: Model configuration
            
        Returns:
            Updated semantic states with mutations applied
        """
        ...


class SuccessDetector(Protocol):
    """Protocol for detecting successful generation trajectories.
    
    Determines when a generation is "successful" enough to warrant
    mutation phase activation, as mentioned in Section 4.3.
    """
    
    @abstractmethod
    def evaluate(
        self, 
        generated_tokens: List[int],
        synthesis_states: List[StateVector],
        target_criteria: Optional[Dict] = None
    ) -> float:
        """Compute success score for generation.
        
        Args:
            generated_tokens: List of generated token IDs
            synthesis_states: History of synthesis states V_{S,t}
            target_criteria: Optional criteria for evaluation
            
        Returns:
            Success score in [0, 1] range
        """
        ...


class MinimalStateEssence(Protocol):
    """Protocol for extracting minimal state essence (MSE).
    
    Implements the Essence extraction described in Section 4.3:
    ε = Essence({V_{S,1}, ..., V_{S,T}})
    """
    
    @abstractmethod
    def extract(
        self,
        synthesis_states: List[StateVector],
        method: str = "mean"
    ) -> StateVector:
        """Extract the minimal shared pattern from synthesis states.
        
        Args:
            synthesis_states: List of synthesis states from successful trajectory
            method: Extraction method ("mean", "max", "learned")
            
        Returns:
            Essence vector ε of shape (d_model,)
        """
        ...
    
    @abstractmethod
    def compute_mse(
        self,
        synthesis_states: List[StateVector],
        success_score: float
    ) -> StateVector:
        """Compute minimal state essence weighted by success.
        
        Args:
            synthesis_states: Synthesis state history
            success_score: Score indicating generation success
            
        Returns:
            Weighted essence vector
        """
        ...


class SummaryFunction(Protocol):
    """Protocol for summary functions used in antithesis generation.
    
    As specified in Algorithm 1, line 4: v_summary = summarize(V_T)
    The summarize function acts as identity in described operational modes
    since both Prefill and Decoding process one state vector at a time.
    """
    def __call__(self, thesis: Tensor, mode: str = "single") -> Tensor:
        """Compute summary vector from thesis.
        
        Args:
            thesis: Thesis vector V_T of shape (batch_size, d_model) for single token
                   or (batch_size, seq_len, d_model) for sequence
            mode: Operation mode - "single" for one token (identity function),
                  "sequence" for multiple tokens (e.g., mean pooling)
                  
        Returns:
            Summary vector v_summary of shape (batch_size, d_model)
        """
        ...


class EssenceExtractor(Protocol):
    """Protocol for essence extraction in mutation phase."""
    def __call__(self, synthesis_states: List[Tensor]) -> Tensor:
        """Extract essence from synthesis states."""
        ...


class FusionOperation(Protocol):
    """Protocol for state fusion in mutation phase."""
    def __call__(self, current_state: Tensor, essence: Tensor, rate: float) -> Tensor:
        """Fuse essence into current state."""
        ...


class DualObjectiveLearner(Protocol):
    """Protocol for dual-objective gradient learning."""
    
    @abstractmethod
    def compute_losses(
        self,
        model_outputs: List[DELTABlockOutput],
        target: Tensor
    ) -> Tuple[Tensor, List[Tensor]]:
        """Compute global and local losses.
        
        Args:
            model_outputs: Outputs from each DELTA block
            target: Target tokens for global loss
            
        Returns:
            Tuple of (global loss, list of local losses per layer)
        """
        ...
    
    @abstractmethod
    def route_gradients(self, model: 'DELTAModel'):
        """Route gradients to appropriate components.
        
        Ensures thesis components only receive local gradients
        and antithesis/control components only receive global gradients.
        """
        ...


class LanguageModelHead(Protocol):
    """Protocol for output projection to vocabulary.
    
    Projects the final hidden states to vocabulary logits for
    token prediction, as used in the decoding phase.
    """
    
    @abstractmethod
    def forward(self, hidden_states: StateVector) -> Tensor:
        """Project hidden states to vocabulary logits.
        
        Args:
            hidden_states: Final hidden states of shape (batch_size, d_model)
            
        Returns:
            Logits over vocabulary of shape (batch_size, vocab_size)
        """
        ...
    
    @abstractmethod
    def get_output_embeddings(self) -> Tensor:
        """Get the output embedding matrix.
        
        Returns:
            Output embeddings of shape (vocab_size, d_model)
        """
        ...
    
    @abstractmethod
    def set_output_embeddings(self, new_embeddings: Tensor):
        """Set new output embeddings.
        
        Args:
            new_embeddings: New embedding matrix of shape (vocab_size, d_model)
        """
        ...


class DELTAModel(Protocol):
    """Protocol for the complete DELTA model."""
    
    @abstractmethod
    def forward(
        self,
        input_ids: Tensor,
        phase: str = "decode",
        kappa_cache: Optional[KappaCache] = None,
        return_details: bool = False
    ) -> Dict:
        """Forward pass through DELTA model.
        
        Args:
            input_ids: Input token IDs
            phase: Operational phase ("prefill", "decode", or "mutate")
            kappa_cache: Optional cache for decode phase
            return_details: Whether to return detailed outputs
            
        Returns:
            Dictionary with outputs appropriate to the phase
        """
        ...
    
    @abstractmethod
    def generate(
        self,
        prompt_ids: Tensor,
        max_length: int = 100,
        temperature: float = 1.0,
        do_mutation: bool = False
    ) -> Tensor:
        """Generate text using the three-phase cycle.
        
        Args:
            prompt_ids: Prompt token IDs
            max_length: Maximum generation length
            temperature: Sampling temperature
            do_mutation: Whether to apply mutation phase after generation
            
        Returns:
            Generated token IDs
        """
        ...
