"""Normalization operations for DELTA architecture.

This module provides various normalization techniques used in DELTA,
including LayerNorm, RMSNorm, and custom normalization methods.
"""

import torch
from torch import Tensor
import torch.nn as nn
import torch.nn.functional as F
from typing import Optional, Tuple, Union

from ..utils.constants import LAYER_NORM_EPS, EPSILON


class LayerNorm(nn.Module):
    """Layer normalization with optional bias and scale."""
    
    def __init__(
        self,
        normalized_shape: Union[int, Tuple[int, ...]],
        eps: float = LAYER_NORM_EPS,
        elementwise_affine: bool = True,
        bias: bool = True
    ):
        super().__init__()
        if isinstance(normalized_shape, int):
            normalized_shape = (normalized_shape,)
        self.normalized_shape = normalized_shape
        self.eps = eps
        self.elementwise_affine = elementwise_affine
        
        if self.elementwise_affine:
            self.weight = nn.Parameter(torch.ones(normalized_shape))
            if bias:
                self.bias = nn.Parameter(torch.zeros(normalized_shape))
            else:
                self.register_parameter('bias', None)
        else:
            self.register_parameter('weight', None)
            self.register_parameter('bias', None)
    
    def forward(self, x: Tensor) -> Tensor:
        """Apply layer normalization.
        
        Args:
            x: Input tensor
            
        Returns:
            Normalized tensor
        """
        return F.layer_norm(
            x, self.normalized_shape, self.weight, self.bias, self.eps
        )


class RMSNorm(nn.Module):
    """Root Mean Square Layer Normalization.
    
    More efficient than LayerNorm as it doesn't compute mean.
    """
    
    def __init__(
        self,
        normalized_shape: Union[int, Tuple[int, ...]],
        eps: float = LAYER_NORM_EPS,
        elementwise_affine: bool = True
    ):
        super().__init__()
        if isinstance(normalized_shape, int):
            normalized_shape = (normalized_shape,)
        self.normalized_shape = normalized_shape
        self.eps = eps
        self.elementwise_affine = elementwise_affine
        
        if self.elementwise_affine:
            self.weight = nn.Parameter(torch.ones(normalized_shape))
        else:
            self.register_parameter('weight', None)
    
    def forward(self, x: Tensor) -> Tensor:
        """Apply RMS normalization.
        
        Args:
            x: Input tensor
            
        Returns:
            Normalized tensor
        """
        # Compute RMS
        rms = torch.sqrt(torch.mean(x ** 2, dim=-1, keepdim=True) + self.eps)
        x_normalized = x / rms
        
        if self.elementwise_affine:
            x_normalized = x_normalized * self.weight
        
        return x_normalized


class GroupNorm(nn.Module):
    """Group normalization for DELTA components."""
    
    def __init__(
        self,
        num_groups: int,
        num_channels: int,
        eps: float = LAYER_NORM_EPS,
        affine: bool = True
    ):
        super().__init__()
        self.num_groups = num_groups
        self.num_channels = num_channels
        self.eps = eps
        self.affine = affine
        
        if self.affine:
            self.weight = nn.Parameter(torch.ones(num_channels))
            self.bias = nn.Parameter(torch.zeros(num_channels))
        else:
            self.register_parameter('weight', None)
            self.register_parameter('bias', None)
    
    def forward(self, x: Tensor) -> Tensor:
        """Apply group normalization.
        
        Args:
            x: Input tensor of shape (batch_size, num_channels, ...)
            
        Returns:
            Normalized tensor
        """
        return F.group_norm(
            x, self.num_groups, self.weight, self.bias, self.eps
        )


class AdaptiveLayerNorm(nn.Module):
    """Adaptive layer normalization with learned conditioning."""
    
    def __init__(
        self,
        normalized_shape: int,
        condition_dim: int,
        eps: float = LAYER_NORM_EPS
    ):
        super().__init__()
        self.normalized_shape = normalized_shape
        self.eps = eps
        
        # Conditioning projections
        self.scale_proj = nn.Linear(condition_dim, normalized_shape)
        self.shift_proj = nn.Linear(condition_dim, normalized_shape)
        
        # Base normalization parameters
        self.weight = nn.Parameter(torch.ones(normalized_shape))
        self.bias = nn.Parameter(torch.zeros(normalized_shape))
    
    def forward(self, x: Tensor, condition: Tensor) -> Tensor:
        """Apply adaptive layer normalization.
        
        Args:
            x: Input tensor
            condition: Conditioning tensor
            
        Returns:
            Adaptively normalized tensor
        """
        # Standard layer norm
        x_norm = F.layer_norm(x, (self.normalized_shape,), None, None, self.eps)
        
        # Adaptive scaling and shifting
        scale = self.scale_proj(condition)  # (batch_size, normalized_shape)
        shift = self.shift_proj(condition)  # (batch_size, normalized_shape)

        # Expand to match input dimensions if needed
        if x.dim() > 2:
            # Add dimensions for sequence length, etc.
            for _ in range(x.dim() - 2):
                scale = scale.unsqueeze(1)
                shift = shift.unsqueeze(1)

        # Apply base parameters and adaptive modulation
        return self.weight * x_norm * (1 + scale) + self.bias + shift


class PowerNorm(nn.Module):
    """Power normalization (generalization of RMSNorm)."""
    
    def __init__(
        self,
        normalized_shape: int,
        power: float = 2.0,
        eps: float = LAYER_NORM_EPS,
        elementwise_affine: bool = True
    ):
        super().__init__()
        self.normalized_shape = normalized_shape
        self.power = power
        self.eps = eps
        
        if elementwise_affine:
            self.weight = nn.Parameter(torch.ones(normalized_shape))
            self.bias = nn.Parameter(torch.zeros(normalized_shape))
        else:
            self.register_parameter('weight', None)
            self.register_parameter('bias', None)
    
    def forward(self, x: Tensor) -> Tensor:
        """Apply power normalization.
        
        Args:
            x: Input tensor
            
        Returns:
            Normalized tensor
        """
        # Compute power mean
        power_mean = torch.mean(torch.abs(x) ** self.power, dim=-1, keepdim=True)
        norm_factor = torch.pow(power_mean + self.eps, 1.0 / self.power)
        
        x_normalized = x / norm_factor
        
        if self.weight is not None:
            x_normalized = x_normalized * self.weight
        if self.bias is not None:
            x_normalized = x_normalized + self.bias
        
        return x_normalized


class ScaleNorm(nn.Module):
    """Scale normalization (learned scaling without centering)."""
    
    def __init__(self, dim: int, eps: float = EPSILON):
        super().__init__()
        self.dim = dim
        self.eps = eps
        self.scale = nn.Parameter(torch.ones(1))
    
    def forward(self, x: Tensor) -> Tensor:
        """Apply scale normalization.
        
        Args:
            x: Input tensor
            
        Returns:
            Scale-normalized tensor
        """
        norm = torch.norm(x, dim=-1, keepdim=True)
        return self.scale * x / (norm + self.eps)


class CrossNorm(nn.Module):
    """Cross-normalization between two streams."""
    
    def __init__(
        self,
        normalized_shape: int,
        eps: float = LAYER_NORM_EPS
    ):
        super().__init__()
        self.normalized_shape = normalized_shape
        self.eps = eps
        
        # Learnable mixing parameters for cross-normalization
        # Initialize to create actual cross-dependencies
        self.alpha = nn.Parameter(torch.tensor(0.7))  # Mix 70% own mean, 30% other mean
        self.beta = nn.Parameter(torch.tensor(0.7))   # Mix 70% own var, 30% other var
    
    def forward(self, x1: Tensor, x2: Tensor) -> Tuple[Tensor, Tensor]:
        """Apply cross-normalization.
        
        Args:
            x1: First input tensor
            x2: Second input tensor
            
        Returns:
            Tuple of cross-normalized tensors
        """
        # Compute statistics from both streams
        mean1 = x1.mean(dim=-1, keepdim=True)
        mean2 = x2.mean(dim=-1, keepdim=True)
        var1 = x1.var(dim=-1, keepdim=True, unbiased=False)
        var2 = x2.var(dim=-1, keepdim=True, unbiased=False)
        
        # Cross-normalize
        x1_norm = (x1 - self.alpha * mean1 - (1 - self.alpha) * mean2) / \
                  torch.sqrt(self.beta * var1 + (1 - self.beta) * var2 + self.eps)
        
        x2_norm = (x2 - self.alpha * mean2 - (1 - self.alpha) * mean1) / \
                  torch.sqrt(self.beta * var2 + (1 - self.beta) * var1 + self.eps)
        
        return x1_norm, x2_norm


def batch_norm_1d(
    x: Tensor,
    running_mean: Optional[Tensor] = None,
    running_var: Optional[Tensor] = None,
    weight: Optional[Tensor] = None,
    bias: Optional[Tensor] = None,
    training: bool = True,
    momentum: float = 0.1,
    eps: float = 1e-5
) -> Tensor:
    """1D batch normalization.
    
    Args:
        x: Input tensor of shape (batch_size, features)
        running_mean: Running mean statistics
        running_var: Running variance statistics
        weight: Scale parameter
        bias: Shift parameter
        training: Whether in training mode
        momentum: Momentum for running statistics
        eps: Epsilon for numerical stability
        
    Returns:
        Batch normalized tensor
    """
    if training:
        mean = x.mean(dim=0)
        var = x.var(dim=0, unbiased=False)
        
        if running_mean is not None:
            running_mean.mul_(1 - momentum).add_(mean * momentum)
        if running_var is not None:
            running_var.mul_(1 - momentum).add_(var * momentum)
    else:
        mean = running_mean if running_mean is not None else x.mean(dim=0)
        var = running_var if running_var is not None else x.var(dim=0, unbiased=False)
    
    x_normalized = (x - mean) / torch.sqrt(var + eps)
    
    if weight is not None:
        x_normalized = x_normalized * weight
    if bias is not None:
        x_normalized = x_normalized + bias
    
    return x_normalized


def instance_norm(
    x: Tensor,
    weight: Optional[Tensor] = None,
    bias: Optional[Tensor] = None,
    eps: float = 1e-5
) -> Tensor:
    """Instance normalization.
    
    Args:
        x: Input tensor
        weight: Scale parameter
        bias: Shift parameter
        eps: Epsilon for numerical stability
        
    Returns:
        Instance normalized tensor
    """
    mean = x.mean(dim=-1, keepdim=True)
    var = x.var(dim=-1, keepdim=True, unbiased=False)
    
    x_normalized = (x - mean) / torch.sqrt(var + eps)
    
    if weight is not None:
        x_normalized = x_normalized * weight
    if bias is not None:
        x_normalized = x_normalized + bias
    
    return x_normalized


def spectral_norm(
    weight: Tensor,
    n_power_iterations: int = 1,
    eps: float = 1e-12
) -> Tensor:
    """Apply spectral normalization to a weight matrix.
    
    Args:
        weight: Weight tensor to normalize
        n_power_iterations: Number of power iterations
        eps: Epsilon for numerical stability
        
    Returns:
        Spectrally normalized weight
    """
    # Flatten weight to 2D
    weight_mat = weight.view(weight.size(0), -1)
    
    # Power iteration to find largest singular value
    u = torch.randn(weight_mat.size(0), 1, device=weight.device)
    v = None
    
    with torch.no_grad():
        for _ in range(n_power_iterations):
            v = torch.matmul(weight_mat.t(), u)
            v = v / (torch.norm(v, dim=0, keepdim=True) + eps)
            u = torch.matmul(weight_mat, v)
            u = u / (torch.norm(u, dim=0, keepdim=True) + eps)
    
    # Compute spectral norm
    sigma = torch.matmul(torch.matmul(u.t(), weight_mat), v)
    
    # Normalize weight
    return weight / (sigma + eps)


# gradient_normalization function removed - not part of DELTA specification
# DELTA achieves gradient stability through dual-objective learning design (Theorem 2)
