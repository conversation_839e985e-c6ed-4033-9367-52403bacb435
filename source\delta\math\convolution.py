"""Convolution operations for DELTA architecture.

This module provides efficient convolution operations used in the frequency-domain
synthesis stage of DELTA blocks.
"""

import torch
import torch.nn as nn
from torch import Tensor
from typing import Optional
import logging

from ..utils.constants import FFT_NORM_MODE, EPSILON
from ..math.advanced.numerical_stability import (
    ensure_numerical_stability,
    safe_log,
    clip_by_value,
    check_numerical_stability
)

logger = logging.getLogger(__name__)


def circular_convolve(
    thesis: Tensor,
    antithesis: Tensor,
    norm: str = FFT_NORM_MODE,
    max_magnitude: float = 1e4,
    stability_check: bool = True
) -> Tensor:
    """Perform circular convolution via FFT with comprehensive numerical stability.

    This is the canonical implementation of the synthesis operation:
    V_S,raw = real(iFFT(FFT(V_T) ⊙ FFT(κ)))

    Args:
        thesis: Thesis vector V_T of shape (batch_size, d_model)
        antithesis: Antithesis vector κ of shape (batch_size, d_model)
        norm: FFT normalization mode ("forward", "backward", or "ortho")
        max_magnitude: Maximum allowed magnitude for intermediate values
        stability_check: Whether to perform comprehensive stability checks

    Returns:
        Raw synthesis vector V_S,raw of shape (batch_size, d_model)
        
    Raises:
        ValueError: If inputs are invalid or have incompatible shapes
        RuntimeError: If FFT operations fail despite stability measures
    """
    # Input validation
    if thesis.shape != antithesis.shape:
        raise ValueError(f"Thesis and antithesis must have same shape, got {thesis.shape} vs {antithesis.shape}")
    
    if thesis.numel() == 0:
        raise ValueError("Input tensors cannot be empty")
    
    # Numerical stability checks
    if stability_check:
        if not check_numerical_stability(thesis, "thesis", raise_on_error=False):
            logger.warning("Thesis contains invalid values, clipping...")
            thesis = clip_by_value(thesis, -max_magnitude, max_magnitude)
            
        if not check_numerical_stability(antithesis, "antithesis", raise_on_error=False):
            logger.warning("Antithesis contains invalid values, clipping...")
            antithesis = clip_by_value(antithesis, -max_magnitude, max_magnitude)

    try:
        # Store original dtype for result preservation
        original_dtype = thesis.dtype
        device = thesis.device
        
        # Use appropriate complex dtype based on input
        if original_dtype == torch.float32:
            complex_dtype = torch.complex64
        elif original_dtype == torch.float64:
            complex_dtype = torch.complex128
        elif original_dtype == torch.float16:
            # Handle half precision by promoting to float32 for FFT
            thesis = thesis.float()
            antithesis = antithesis.float()
            complex_dtype = torch.complex64
        else:
            complex_dtype = torch.complex64  # Default fallback

        # Forward FFT with stability monitoring
        thesis_fft = torch.fft.fft(thesis, dim=-1, norm=norm)
        antithesis_fft = torch.fft.fft(antithesis, dim=-1, norm=norm)
        
        # Check for FFT overflow
        if stability_check:
            thesis_fft_mag = torch.abs(thesis_fft)
            antithesis_fft_mag = torch.abs(antithesis_fft)
            
            if torch.max(thesis_fft_mag) > max_magnitude:
                logger.warning("Thesis FFT magnitude too large, scaling down...")
                scale_factor = max_magnitude / torch.max(thesis_fft_mag)
                thesis_fft = thesis_fft * scale_factor
                
            if torch.max(antithesis_fft_mag) > max_magnitude:
                logger.warning("Antithesis FFT magnitude too large, scaling down...")
                scale_factor = max_magnitude / torch.max(antithesis_fft_mag)
                antithesis_fft = antithesis_fft * scale_factor

        # Hadamard product in frequency domain
        synthesis_fft = thesis_fft * antithesis_fft
        
        # Additional stability check on product
        if stability_check:
            product_mag = torch.abs(synthesis_fft)
            if torch.max(product_mag) > max_magnitude:
                logger.warning("FFT product magnitude too large, scaling down...")
                scale_factor = max_magnitude / torch.max(product_mag)
                synthesis_fft = synthesis_fft * scale_factor

        # Inverse FFT and take real part
        synthesis_complex = torch.fft.ifft(synthesis_fft, dim=-1, norm=norm)
        synthesis_raw = synthesis_complex.real
        
        # Convert back to original dtype while preserving gradients
        synthesis_raw = synthesis_raw.to(original_dtype)
        
        # Final stability check and cleanup
        if stability_check:
            synthesis_raw = ensure_numerical_stability(synthesis_raw, "circular_convolution_output")
            synthesis_raw = clip_by_value(synthesis_raw, -max_magnitude, max_magnitude)
        
        return synthesis_raw
        
    except Exception as e:
        logger.error(f"FFT convolution failed: {e}")
        
        # Fallback to element-wise product with proper scaling
        logger.warning("Falling back to element-wise product for synthesis")
        
        # Normalize inputs to prevent overflow
        thesis_norm = torch.norm(thesis, dim=-1, keepdim=True)
        antithesis_norm = torch.norm(antithesis, dim=-1, keepdim=True)
        
        # Avoid division by zero
        thesis_norm = torch.clamp(thesis_norm, min=EPSILON)
        antithesis_norm = torch.clamp(antithesis_norm, min=EPSILON)
        
        thesis_normalized = thesis / thesis_norm
        antithesis_normalized = antithesis / antithesis_norm
        
        # Element-wise product with geometric mean of norms
        fallback_result = thesis_normalized * antithesis_normalized
        combined_norm = torch.sqrt(thesis_norm * antithesis_norm)
        
        return fallback_result * combined_norm


class LearnableConvolution(nn.Module):
    """Learnable FFT convolution module with numerical stability."""

    def __init__(self, d_model: int, bias: bool = True, stability_check: bool = True):
        super().__init__()
        self.d_model = d_model
        self.stability_check = stability_check

        # Learnable frequency-domain kernel
        self.kernel_real = nn.Parameter(torch.randn(d_model) * 0.1)
        self.kernel_imag = nn.Parameter(torch.randn(d_model) * 0.1)
        
        if bias:
            self.bias = nn.Parameter(torch.zeros(d_model))
        else:
            self.register_parameter('bias', None)

        self._reset_parameters()

    def _reset_parameters(self):
        """Initialize parameters for stability."""
        # Initialize to approximate identity
        nn.init.normal_(self.kernel_real, mean=1.0, std=0.1)
        nn.init.normal_(self.kernel_imag, mean=0.0, std=0.01)
        
        if self.bias is not None:
            nn.init.zeros_(self.bias)

    def forward(self, x: Tensor) -> Tensor:
        """Apply learnable convolution with stability checks."""
        # Create complex kernel
        kernel = torch.complex(self.kernel_real, self.kernel_imag)
        
        # Apply circular convolution using the kernel
        x_fft = torch.fft.fft(x.to(torch.complex64), dim=-1, norm='ortho')
        
        # Apply learnable kernel in frequency domain
        output_fft = x_fft * kernel.unsqueeze(0)
        
        # Transform back to time domain
        output = torch.fft.ifft(output_fft, dim=-1, norm='ortho').real
        
        # Add bias if present
        if self.bias is not None:
            output = output + self.bias
        
        # Apply stability check
        if self.stability_check:
            output = ensure_numerical_stability(output, "learnable_convolution")
            
        return output
