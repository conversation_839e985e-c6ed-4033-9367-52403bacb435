"""Type definitions and aliases for the DELTA decoder architecture.

This module defines custom types used throughout the DELTA implementation,
including tensor types, configuration types, and domain-specific aliases.
"""

from typing import Dict, List, Optional, Tuple, Union, NamedTuple, TypeVar
from dataclasses import dataclass
import torch
from torch import Tensor

# Import the unified KappaCache from cache module
from ...cache.kappa_cache import KappaCache

# Type aliases for clarity
StateVector = Tensor  # Shape: (batch_size, d_model)
SequenceStates = Tensor  # Shape: (batch_size, seq_len, d_model)
AttractorStates = List[Tensor]  # List of hidden states for semantic attractors (single layer)
LayerAttractorStates = List[List[Tensor]]  # List of AttractorStates for all layers (standardized format)
LayerIndex = int
TokenIndex = int
BatchSize = int
SeqLength = int
ModelDim = int

# Generic type for different attractor types
T = TypeVar('T')


@dataclass
class DELTAConfig:
    """Configuration for the DELTA model architecture."""
    d_model: int = 512  # Model dimension
    n_layers: int = 12  # Number of DELTA blocks
    n_attractors: int = 8  # Number of attractors per domain
    n_heads: int = 8  # Number of attention heads for cache attention
    dropout: float = 0.1
    max_seq_length: int = 2048
    vocab_size: int = 50257
    pad_token_id: int = 0

    # Attractor-specific configs
    semantic_hidden_size: int = 256  # Hidden size for semantic attractors (GRUs)
    structural_proj_size: int = 256  # Projection size for structural attractors

    # Learning rates for dual-objective
    thesis_lr: float = 1e-3
    antithesis_lr: float = 1e-3

    # Mutation phase parameters
    mutation_rate: float = 0.1
    essence_extraction_method: str = "mean"  # "mean", "max", "learned"
    essence_pool_size: int = 100

    # Additional configuration options for DELTABlock
    layer_norm_eps: float = 1e-5  # LayerNorm epsilon
    ffn_expansion_ratio: int = 4  # FFN hidden dimension multiplier
    ffn_activation: str = "gelu"  # FFN activation function
    thesis_dropout_enabled: bool = False  # Whether to use dropout in thesis generation (non-standard)


@dataclass
class AttractorOutput:
    """Output from an attractor computation."""
    force: Tensor  # The computed force vector
    hidden_state: Optional[Tensor] = None  # Updated hidden state (for semantic attractors)
    gate_weight: Optional[float] = None  # Gating weight applied to this attractor


@dataclass
class DELTABlockOutput:
    """Output from a single DELTA block forward pass."""
    output_state: StateVector  # h_out
    thesis: StateVector  # V_T
    local_antithesis: StateVector  # κ (local composite, pre-cache-fusion)
    kappa_effective: Optional[StateVector] = None  # κ_effective (post-cache-fusion, used in synthesis)
    synthesis_raw: StateVector = None  # V_S,raw
    synthesis_refined: StateVector = None  # V_S
    semantic_states: AttractorStates = None  # Updated semantic attractor states
    
    # Optional detailed outputs for analysis
    structural_forces: Optional[List[Tensor]] = None
    semantic_forces: Optional[List[Tensor]] = None
    gate_weights_struct: Optional[Tensor] = None
    gate_weights_sem: Optional[Tensor] = None


@dataclass
class PhaseOutput:
    """Output from an operational phase."""
    final_state: StateVector
    semantic_states: Optional[LayerAttractorStates] = None  # Updated semantic states (standardized format)
    kappa_cache: Optional[KappaCache] = None  # Populated during prefill
    generated_tokens: Optional[List[int]] = None  # For decoding phase
    mutation_applied: bool = False
    success_score: Optional[float] = None  # For mutation phase


@dataclass
class TrainingMetrics:
    """Metrics collected during training."""
    global_loss: float
    local_losses: List[float]  # Per-layer tension losses
    thesis_grad_norm: float
    antithesis_grad_norm: float
    accuracy: Optional[float] = None
    perplexity: Optional[float] = None

# Operational phase types
class Phase:
    """Enumeration of operational phases."""
    PREFILL = "prefill"
    DECODE = "decode"
    MUTATE = "mutate"


# Loss function types  
class LossType:
    """Types of losses in dual-objective learning."""
    GLOBAL = "global"  # Task loss
    LOCAL = "local"    # Tension loss


# Component types for gradient routing
class ComponentType:
    """Types of components for gradient routing."""
    THESIS = "thesis"
    ANTITHESIS = "antithesis"
    CONTROL = "control"  # Gating, update gates, etc.
