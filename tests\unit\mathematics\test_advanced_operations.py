"""
Comprehensive test suite for advanced mathematical operations in DELTA architecture.

This module tests advanced mathematical operations including gradient manipulation,
numerical stability, and tensor optimizations critical for DELTA's dual-objective
learning and robust processing.

Tests follow TDD principles and validate specification adherence.
"""

import pytest
import torch
import numpy as np
from typing import Tuple, Optional, List, Callable
import math

from source.delta.math.advanced.gradient_blocking import (
    detach_tensor,
    selective_detach,
    gradient_reversal,
    scale_gradient,
    stop_gradient,
    conditional_detach,
    DualPathGradient,
    gradient_checkpoint,
    GradientClipping,
    create_gradient_mask,
    asymmetric_operation,
    dual_objective_update,
    GradientModulation
)

from source.delta.math.advanced.numerical_stability import (
    safe_log,
    safe_sqrt,
    safe_divide,
    safe_norm,
    clip_by_value,
    clip_by_norm,
    stable_softmax,
    stable_log_softmax,
    prevent_gradient_explosion,
    stable_layer_norm,
    stable_gelu,
    stable_sigmoid,
    stable_tanh,
    NumericalStabilizer,
    check_numerical_stability,
    compute_bounded_attention_scores,
    stabilize_gradients,
    stable_cross_entropy
)

from source.delta.math.advanced.circular_convolution import (
    multi_scale_fft_convolution,
    adaptive_fft_convolution,
    grouped_fft_convolution,
    dilated_fft_convolution,
    spectral_gated_convolution,
    HierarchicalFFTConvolution,
    fast_hadamard_product,
    circular_convolution_2d,
    optimized_circular_convolution
)

# tensor_optimizations imports removed - not part of DELTA specification


class TestGradientBlocking:
    """Test gradient blocking mechanisms for DELTA's dual-objective learning."""
    
    def setup_method(self):
        """Set up test fixtures."""
        torch.manual_seed(42)
        self.batch_size = 4
        self.d_model = 64
        
        self.thesis = torch.randn(self.batch_size, self.d_model, requires_grad=True)
        self.antithesis = torch.randn(self.batch_size, self.d_model, requires_grad=True)
        self.local_loss_target = torch.randn(self.batch_size, self.d_model)
    
    def test_detach_tensor_basic(self):
        """Test basic tensor detachment for gradient blocking."""
        detached = detach_tensor(self.antithesis)
        
        # Should have same values but no gradients
        torch.testing.assert_close(detached, self.antithesis)
        assert not detached.requires_grad
        
        # Gradient flow should be blocked
        loss = (self.thesis - detached).sum()
        loss.backward()
        
        assert self.thesis.grad is not None
        assert self.antithesis.grad is None  # Should not receive gradients
    
    def test_selective_detach(self):
        """Test selective detachment based on conditions."""
        # Create condition (detach half the batch)
        condition = torch.tensor([True, False, True, False])
        
        result = selective_detach(self.thesis, condition)
        
        # Shape should be preserved
        assert result.shape == self.thesis.shape
        
        # Test gradient flow
        loss = result.sum()
        loss.backward()
        
        # Only non-detached elements should receive gradients
        expected_grad = torch.zeros_like(self.thesis)
        expected_grad[~condition] = 1.0  # Only these positions get gradients
        
        # Note: actual implementation details may vary
        assert self.thesis.grad is not None
    
    def test_gradient_reversal(self):
        """Test gradient reversal for adversarial training."""
        # Apply gradient reversal
        reversed_tensor = gradient_reversal(self.thesis, lambda_=1.0)
        
        # Forward pass should be identity
        torch.testing.assert_close(reversed_tensor, self.thesis)
        
        # Backward pass should reverse gradients
        loss = reversed_tensor.sum()
        loss.backward()
        
        # Gradients should be reversed (negative)
        assert self.thesis.grad is not None
        expected_grad = -torch.ones_like(self.thesis)
        torch.testing.assert_close(self.thesis.grad, expected_grad, rtol=1e-5, atol=1e-6)
    
    def test_scale_gradient(self):
        """Test gradient scaling."""
        scales = [0.1, 2.0, 10.0]
        
        for scale in scales:
            self.thesis.grad = None  # Reset gradients
            
            scaled_tensor = scale_gradient(self.thesis, scale)
            
            # Forward pass should be identity
            torch.testing.assert_close(scaled_tensor, self.thesis)
            
            # Backward pass should scale gradients
            loss = scaled_tensor.sum()
            loss.backward()
            
            expected_grad = torch.ones_like(self.thesis) * scale
            torch.testing.assert_close(self.thesis.grad, expected_grad, rtol=1e-5, atol=1e-6)
    
    def test_stop_gradient(self):
        """Test complete gradient stopping."""
        stopped = stop_gradient(self.thesis)
        
        # Forward pass should be identity
        torch.testing.assert_close(stopped, self.thesis)
        
        # The stopped tensor should still require gradients but block flow
        assert stopped.requires_grad
        
        # No gradients should flow to original tensor
        loss = stopped.sum()
        loss.backward()
        
        # Original tensor should not receive gradients
        assert self.thesis.grad is None or torch.allclose(self.thesis.grad, torch.zeros_like(self.thesis))
    
    def test_conditional_detach(self):
        """Test conditional gradient detachment."""
        # Create various conditions
        conditions = [
            lambda x: x.sum() > 0,  # Always true for our random tensor
            lambda x: x.sum() < 0,  # Always false for our random tensor
            lambda x: True,         # Always detach
            lambda x: False,        # Never detach
        ]
        
        for i, condition in enumerate(conditions):
            self.thesis.grad = None  # Reset
            
            result = conditional_detach(self.thesis, condition)
            
            # Check if tensor should be detached based on condition
            should_detach = condition(self.thesis) if callable(condition) else condition
            
            if should_detach:
                # If detached, result should not require gradients
                assert not result.requires_grad
                # Original tensor grad should remain None after attempting backward
                if result.requires_grad:  # Only call backward if tensor requires grad
                    loss = result.sum()
                    loss.backward()
                assert self.thesis.grad is None
            else:
                # If not detached, gradients should flow normally
                assert result.requires_grad
                loss = result.sum()
                loss.backward()
                assert self.thesis.grad is not None


class TestDualPathGradient:
    """Test dual-path gradient mechanism for DELTA's dual-objective learning."""
    
    def setup_method(self):
        """Set up test fixtures."""
        torch.manual_seed(42)
        self.batch_size = 4
        self.d_model = 64
        
        self.thesis = torch.randn(self.batch_size, self.d_model)
        self.antithesis = torch.randn(self.batch_size, self.d_model)
        self.dual_path = DualPathGradient()
    
    def test_dual_path_forward(self):
        """Test dual path gradient forward pass."""
        def synthesis_fn(thesis, antithesis):
            return thesis + antithesis
        
        result = self.dual_path(self.thesis, self.antithesis, synthesis_fn)
        
        # Output shape should match inputs
        assert result.shape == self.thesis.shape
        assert torch.isfinite(result).all()
    
    def test_dual_path_gradient_separation(self):
        """Test that dual paths create separate gradient flows."""
        thesis_param = torch.nn.Parameter(self.thesis.clone())
        antithesis_param = torch.nn.Parameter(self.antithesis.clone())
        
        def synthesis_fn(thesis, antithesis):
            return thesis + antithesis
        
        # Simulate dual objective training
        result = self.dual_path(thesis_param, antithesis_param, synthesis_fn)
        
        # Local loss (for thesis)
        local_loss = ((result - self.antithesis.detach()) ** 2).sum()
        
        # Global loss (for antithesis)
        global_loss = result.sum()
        
        # Test separate backward passes
        local_loss.backward(retain_graph=True)
        thesis_local_grad = thesis_param.grad.clone() if thesis_param.grad is not None else None
        
        thesis_param.grad = None
        antithesis_param.grad = None
        
        global_loss.backward()
        thesis_global_grad = thesis_param.grad.clone() if thesis_param.grad is not None else None
        antithesis_global_grad = antithesis_param.grad.clone() if antithesis_param.grad is not None else None
        
        # Different paths should produce different gradients
        if thesis_local_grad is not None and thesis_global_grad is not None:
            assert not torch.allclose(thesis_local_grad, thesis_global_grad, rtol=1e-3)
    
    def test_asymmetric_operation(self):
        """Test asymmetric operations for different gradient paths."""
        forward_fn = lambda x: torch.relu(x)
        backward_fn = lambda x, grad_output: torch.sigmoid(x) * grad_output
        
        result = asymmetric_operation(self.thesis, forward_fn, backward_fn)
        
        # Forward should apply forward_fn
        expected_forward = forward_fn(self.thesis)
        torch.testing.assert_close(result, expected_forward)
        
        # Test that it's differentiable
        thesis_param = torch.nn.Parameter(self.thesis.clone())
        asymm_result = asymmetric_operation(thesis_param, forward_fn, backward_fn)
        loss = asymm_result.sum()
        loss.backward()
        
        assert thesis_param.grad is not None
        assert torch.isfinite(thesis_param.grad).all()


class TestNumericalStability:
    """Test numerical stability operations for robust DELTA processing."""
    
    def setup_method(self):
        """Set up test fixtures."""
        torch.manual_seed(42)
        self.batch_size = 4
        self.d_model = 64
        
        # Create test tensors with potential numerical issues
        self.normal_tensor = torch.randn(self.batch_size, self.d_model)
        self.small_tensor = torch.full((self.batch_size, self.d_model), 1e-10)
        self.large_tensor = torch.full((self.batch_size, self.d_model), 1e10)
        self.zero_tensor = torch.zeros(self.batch_size, self.d_model)
        self.negative_tensor = torch.full((self.batch_size, self.d_model), -1e-5)
    
    def test_safe_log(self):
        """Test numerically safe logarithm."""
        # Test with normal values
        normal_result = safe_log(self.normal_tensor.abs() + 1)
        assert torch.isfinite(normal_result).all()
        
        # Test with very small values
        small_result = safe_log(self.small_tensor)
        assert torch.isfinite(small_result).all()
        assert not torch.isinf(small_result).any()
        
        # Test with zero values
        zero_result = safe_log(self.zero_tensor)
        assert torch.isfinite(zero_result).all()
        
        # Should be equivalent to torch.log for reasonable values
        reasonable = torch.abs(self.normal_tensor) + 1
        torch_log = torch.log(reasonable)
        safe_result = safe_log(reasonable)
        torch.testing.assert_close(torch_log, safe_result, rtol=1e-5, atol=1e-6)
    
    def test_safe_sqrt(self):
        """Test numerically safe square root."""
        # Test with positive values
        positive = torch.abs(self.normal_tensor)
        sqrt_result = safe_sqrt(positive)
        assert torch.isfinite(sqrt_result).all()
        assert (sqrt_result >= 0).all()
        
        # Test with negative values
        negative_result = safe_sqrt(self.negative_tensor)
        assert torch.isfinite(negative_result).all()
        assert (negative_result >= 0).all()  # Should clamp negatives to 0
        
        # Test with zero
        zero_result = safe_sqrt(self.zero_tensor)
        assert torch.allclose(zero_result, torch.zeros_like(zero_result))
    
    def test_safe_divide(self):
        """Test numerically safe division."""
        numerator = self.normal_tensor
        
        # Safe division by normal denominator
        normal_denom = torch.randn(self.batch_size, self.d_model) + 2  # Avoid zeros
        normal_result = safe_divide(numerator, normal_denom)
        expected = numerator / normal_denom
        torch.testing.assert_close(normal_result, expected, rtol=1e-5, atol=1e-6)
        
        # Safe division by very small denominator
        small_result = safe_divide(numerator, self.small_tensor)
        assert torch.isfinite(small_result).all()
        assert not torch.isinf(small_result).any()
        
        # Safe division by zero
        zero_result = safe_divide(numerator, self.zero_tensor)
        assert torch.isfinite(zero_result).all()
    
    def test_safe_norm(self):
        """Test numerically safe norm computation."""
        # Test L2 norm
        l2_norm = safe_norm(self.normal_tensor, p=2, dim=-1)
        assert l2_norm.shape == (self.batch_size,)
        assert torch.isfinite(l2_norm).all()
        assert (l2_norm >= 0).all()
        
        # Test L1 norm
        l1_norm = safe_norm(self.normal_tensor, p=1, dim=-1)
        assert torch.isfinite(l1_norm).all()
        assert (l1_norm >= 0).all()
        
        # Test with extreme values
        large_norm = safe_norm(self.large_tensor, p=2, dim=-1)
        assert torch.isfinite(large_norm).all()
        
        small_norm = safe_norm(self.small_tensor, p=2, dim=-1)
        assert torch.isfinite(small_norm).all()
    
    def test_clip_by_value(self):
        """Test value clipping for numerical stability."""
        min_val, max_val = -5.0, 5.0
        
        # Create tensor with extreme values
        extreme_tensor = torch.tensor([-100.0, -1.0, 0.0, 1.0, 100.0])
        clipped = clip_by_value(extreme_tensor, min_val, max_val)
        
        assert (clipped >= min_val).all()
        assert (clipped <= max_val).all()
        
        # Values within range should be unchanged
        within_range = torch.tensor([-1.0, 0.0, 1.0])
        clipped_range = clip_by_value(within_range, min_val, max_val)
        torch.testing.assert_close(clipped_range, within_range)
    
    def test_clip_by_norm(self):
        """Test norm clipping for gradient stability."""
        max_norm = 1.0
        
        # Create tensor with large norm
        large_norm_tensor = torch.randn(self.batch_size, self.d_model) * 10
        clipped = clip_by_norm(large_norm_tensor, max_norm, dim=-1)
        
        # Clipped norms should not exceed max_norm
        clipped_norms = torch.norm(clipped, p=2, dim=-1)
        assert (clipped_norms <= max_norm + 1e-6).all()  # Small tolerance for numerical precision
        
        # Small norm tensors should be unchanged
        small_norm_tensor = torch.randn(self.batch_size, self.d_model) * 0.1
        clipped_small = clip_by_norm(small_norm_tensor, max_norm, dim=-1)
        torch.testing.assert_close(clipped_small, small_norm_tensor, rtol=1e-5, atol=1e-6)
    
    def test_stable_softmax(self):
        """Test numerically stable softmax."""
        # Test with normal values
        normal_softmax = stable_softmax(self.normal_tensor, dim=-1)
        assert normal_softmax.shape == self.normal_tensor.shape
        assert (normal_softmax >= 0).all()
        assert torch.allclose(normal_softmax.sum(dim=-1), torch.ones(self.batch_size))
        
        # Test with large values
        large_softmax = stable_softmax(self.large_tensor, dim=-1)
        assert torch.isfinite(large_softmax).all()
        assert not torch.isnan(large_softmax).any()
        
        # Compare with PyTorch softmax for reasonable values
        torch_softmax = torch.softmax(self.normal_tensor, dim=-1)
        torch.testing.assert_close(normal_softmax, torch_softmax, rtol=1e-4, atol=1e-5)
    
    def test_stable_log_softmax(self):
        """Test numerically stable log-softmax."""
        log_softmax_result = stable_log_softmax(self.normal_tensor, dim=-1)
        
        assert log_softmax_result.shape == self.normal_tensor.shape
        assert torch.isfinite(log_softmax_result).all()
        assert (log_softmax_result <= 0).all()  # Log probabilities should be ≤ 0
        
        # Should be equivalent to log(softmax(x))
        softmax_result = stable_softmax(self.normal_tensor, dim=-1)
        expected = safe_log(softmax_result)
        torch.testing.assert_close(log_softmax_result, expected, rtol=1e-4, atol=1e-5)
    
    def test_prevent_gradient_explosion(self):
        """Test gradient explosion prevention."""
        # Create parameter with potentially exploding gradients
        param = torch.nn.Parameter(self.normal_tensor.clone())
        
        # Simulate large gradients
        large_grad = torch.randn_like(param) * 100
        param.grad = large_grad
        
        # Prevent explosion
        prevent_gradient_explosion(param, max_norm=1.0)
        
        # Gradient norm should be clipped
        grad_norm = torch.norm(param.grad)
        assert grad_norm <= 1.0 + 1e-6
    
    def test_stable_activations(self):
        """Test numerically stable activation functions."""
        activations = [stable_gelu, stable_sigmoid, stable_tanh]
        
        for activation in activations:
            # Test with normal values
            normal_result = activation(self.normal_tensor)
            assert torch.isfinite(normal_result).all()
            
            # Test with extreme values
            large_result = activation(self.large_tensor)
            assert torch.isfinite(large_result).all()
            
            small_result = activation(self.small_tensor)
            assert torch.isfinite(small_result).all()


class TestNumericalStabilizer:
    """Test the NumericalStabilizer module for comprehensive stability."""
    
    def setup_method(self):
        """Set up test fixtures."""
        torch.manual_seed(42)
        self.batch_size = 4
        self.d_model = 64
        self.stabilizer = NumericalStabilizer()
        
        self.normal_tensor = torch.randn(self.batch_size, self.d_model)
        self.unstable_tensor = torch.tensor([float('inf'), float('-inf'), float('nan'), 1e10, -1e10, 0.0])
    
    def test_stabilizer_check_stability(self):
        """Test numerical stability checking."""
        # Normal tensor should be stable
        is_stable = check_numerical_stability(self.normal_tensor)
        assert is_stable
        
        # Unstable tensor should be detected (use raise_on_error=False to get boolean return)
        is_unstable = check_numerical_stability(self.unstable_tensor, raise_on_error=False)
        assert not is_unstable
    
    def test_stabilizer_fix_tensor(self):
        """Test tensor stabilization."""
        # Use forward method (standard PyTorch interface) instead of non-existent stabilize_tensor
        stabilized = self.stabilizer(self.unstable_tensor)

        # Should fix all numerical issues
        assert torch.isfinite(stabilized).all()
        assert not torch.isnan(stabilized).any()
        assert not torch.isinf(stabilized).any()
    
    def test_stabilizer_attention_scores(self):
        """Test bounded attention score computation."""
        # Create potentially problematic attention scores
        query = torch.randn(self.batch_size, 8, self.d_model) * 10
        key = torch.randn(self.batch_size, 8, self.d_model) * 10
        
        scores = compute_bounded_attention_scores(query, key, max_value=10.0)
        
        assert torch.isfinite(scores).all()
        assert (scores.abs() <= 10.0).all()
    
    def test_stabilize_gradients(self):
        """Test gradient stabilization."""
        # Create a simple module with parameters
        module = torch.nn.Linear(self.d_model, self.d_model)

        # Apply gradient stabilization hooks
        stabilize_gradients(module)

        # Create input that will produce large gradients
        large_input = self.normal_tensor * 1e6  # Very large input
        output = module(large_input)

        # Create loss that will produce problematic gradients
        loss = (output ** 3).sum()  # Cubic loss for large gradients
        loss.backward()

        # Check that gradients are stabilized
        for param in module.parameters():
            if param.grad is not None:
                assert torch.isfinite(param.grad).all()
                assert not torch.isnan(param.grad).any()
                assert (param.grad.abs() <= 1e3).all()  # Should be clipped to 1e3


class TestCircularConvolutionAdvanced:
    """Test advanced circular convolution operations."""
    
    def setup_method(self):
        """Set up test fixtures."""
        torch.manual_seed(42)
        self.batch_size = 4
        self.d_model = 64
        
        self.thesis = torch.randn(self.batch_size, self.d_model)
        self.antithesis = torch.randn(self.batch_size, self.d_model)
    
    def test_multi_scale_fft_convolution(self):
        """Test multi-scale FFT convolution."""
        scales = [2, 4, 8]
        result = multi_scale_fft_convolution(self.thesis, self.antithesis, scales)
        
        assert result.shape == self.thesis.shape
        assert torch.isfinite(result).all()
    
    def test_adaptive_fft_convolution(self):
        """Test adaptive FFT convolution."""
        # Test with adaptation parameter (create proper tensor and use correct parameter name)
        adaptation_signal = torch.randn(self.batch_size, self.d_model) * 0.5
        result = adaptive_fft_convolution(
            self.thesis, self.antithesis, adaptation_signal=adaptation_signal
        )
        
        assert result.shape == self.thesis.shape
        assert torch.isfinite(result).all()
        
        # Different adaptation signals should give different results
        adaptation_weak = torch.randn(self.batch_size, self.d_model) * 0.1
        adaptation_strong = torch.randn(self.batch_size, self.d_model) * 0.9

        result_weak = adaptive_fft_convolution(
            self.thesis, self.antithesis, adaptation_signal=adaptation_weak
        )
        result_strong = adaptive_fft_convolution(
            self.thesis, self.antithesis, adaptation_signal=adaptation_strong
        )
        
        assert not torch.allclose(result_weak, result_strong, rtol=1e-3)
    
    def test_grouped_fft_convolution(self):
        """Test grouped FFT convolution."""
        num_groups = 4
        assert self.d_model % num_groups == 0  # Ensure divisible
        
        result = grouped_fft_convolution(
            self.thesis, self.antithesis, n_groups=num_groups
        )
        
        assert result.shape == self.thesis.shape
        assert torch.isfinite(result).all()
    
    def test_dilated_fft_convolution(self):
        """Test dilated FFT convolution."""
        dilation_rates = [1, 2, 4]
        
        for dilation in dilation_rates:
            result = dilated_fft_convolution(
                self.thesis, self.antithesis, dilation_factor=dilation
            )
            
            assert result.shape == self.thesis.shape
            assert torch.isfinite(result).all()
    
    def test_spectral_gated_convolution(self):
        """Test spectral gated convolution."""
        result = spectral_gated_convolution(self.thesis, self.antithesis)
        
        assert result.shape == self.thesis.shape
        assert torch.isfinite(result).all()
    
    def test_hierarchical_fft_convolution(self):
        """Test hierarchical FFT convolution module."""
        conv_module = HierarchicalFFTConvolution(self.d_model, n_levels=3)
        
        result = conv_module(self.thesis, self.antithesis)
        
        assert result.shape == self.thesis.shape
        assert torch.isfinite(result).all()
        
        # Test gradient flow
        thesis_param = torch.nn.Parameter(self.thesis.clone())
        antithesis_param = torch.nn.Parameter(self.antithesis.clone())
        
        output = conv_module(thesis_param, antithesis_param)
        loss = output.sum()
        loss.backward()
        
        assert thesis_param.grad is not None
        assert antithesis_param.grad is not None
    
    def test_fast_hadamard_product(self):
        """Test fast Hadamard product computation."""
        result = fast_hadamard_product(self.thesis, self.antithesis)
        
        # Should be equivalent to element-wise multiplication
        expected = self.thesis * self.antithesis
        torch.testing.assert_close(result, expected, rtol=1e-5, atol=1e-6)
    
    def test_circular_convolution_2d(self):
        """Test 2D circular convolution."""
        # Create 2D tensors
        thesis_2d = torch.randn(self.batch_size, 8, 8)
        antithesis_2d = torch.randn(self.batch_size, 8, 8)
        
        result = circular_convolution_2d(thesis_2d, antithesis_2d, height=8, width=8)

        # Function flattens 2D result to [batch_size, height*width]
        expected_shape = (self.batch_size, 8 * 8)
        assert result.shape == expected_shape
        assert torch.isfinite(result).all()
    
    def test_optimized_circular_convolution(self):
        """Test optimized circular convolution implementation."""
        # Function returns tuple (result, optional_cache)
        result, cache = optimized_circular_convolution(self.thesis, self.antithesis)

        assert result.shape == self.thesis.shape
        assert torch.isfinite(result).all()
        assert cache is None  # No caching requested
        
        # Should produce similar results to basic implementation
        # (This would need the basic implementation for comparison)


# TestTensorOptimizations class removed - these functions are not part of the DELTA specification
# The DELTA architecture uses standard linear projections, LayerNorm, FFN, and specific attention
# mechanisms as defined in the specification, not these optimization utilities.


class TestAdvancedMathIntegration:
    """Integration tests for advanced math operations in DELTA context."""
    
    def setup_method(self):
        """Set up test fixtures for integration testing."""
        torch.manual_seed(42)
        self.batch_size = 4
        self.d_model = 128
        self.seq_len = 16
        
        # DELTA components
        self.V_T = torch.randn(self.batch_size, self.d_model)
        self.kappa = torch.randn(self.batch_size, self.d_model)
        self.h_in = torch.randn(self.batch_size, self.d_model)
    
    def test_delta_local_tension_loss(self):
        """Test local tension loss computation with gradient blocking."""
        # Local tension loss: ||V_T - detach(κ_effective)||²
        kappa_detached = detach_tensor(self.kappa)
        
        local_loss = torch.norm(self.V_T - kappa_detached, p=2, dim=-1) ** 2
        assert local_loss.shape == (self.batch_size,)
        assert torch.isfinite(local_loss).all()
        assert (local_loss >= 0).all()
    
    def test_dual_objective_training_step(self):
        """Test complete dual-objective training step."""
        # Create parameters for thesis and antithesis
        thesis_params = torch.nn.Parameter(torch.randn(self.d_model, self.d_model))
        antithesis_params = torch.nn.Parameter(torch.randn(self.d_model, self.d_model))
        
        # Forward pass
        V_T = torch.matmul(self.h_in, thesis_params)
        kappa = torch.matmul(self.h_in, antithesis_params)
        
        # Synthesis with numerical stability
        synthesis, _ = optimized_circular_convolution(V_T, kappa)  # Unpack tuple
        synthesis = stable_layer_norm(synthesis, normalized_shape=(synthesis.shape[-1],))
        
        # Dual objectives
        global_loss = synthesis.sum()  # Global task loss
        local_loss = torch.norm(V_T - detach_tensor(kappa), p=2) ** 2  # Local tension loss
        
        # Separate gradient updates (DELTA dual-objective learning)
        dual_objective_update(
            thesis_params=torch.nn.ParameterList([thesis_params]),
            antithesis_params=torch.nn.ParameterList([antithesis_params]),
            local_loss=local_loss,
            global_loss=global_loss
        )
        
        # Check that gradients were computed
        assert thesis_params.grad is not None
        assert antithesis_params.grad is not None
        assert torch.isfinite(thesis_params.grad).all()
        assert torch.isfinite(antithesis_params.grad).all()
    
    def test_numerical_stability_in_synthesis(self):
        """Test numerical stability throughout synthesis pipeline."""
        # Start with potentially unstable inputs
        large_V_T = self.V_T * 1e3
        small_kappa = self.kappa * 1e-6
        
        # Apply stability measures
        stabilized_V_T = clip_by_norm(large_V_T, max_norm=10.0, dim=-1)
        stabilized_kappa = safe_divide(small_kappa, torch.norm(small_kappa, dim=-1, keepdim=True))
        
        # Synthesis with stability
        synthesis, _ = optimized_circular_convolution(stabilized_V_T, stabilized_kappa)  # Unpack tuple
        synthesis = stable_layer_norm(synthesis, normalized_shape=(synthesis.shape[-1],))
        
        # Final result should be stable
        assert torch.isfinite(synthesis).all()
        assert not torch.isnan(synthesis).any()
        assert check_numerical_stability(synthesis)
    
    def test_gradient_flow_with_blocking(self):
        """Test gradient flow with selective blocking."""
        # Create computation graph with selective detachment
        thesis_param = torch.nn.Parameter(self.V_T.clone())
        antithesis_param = torch.nn.Parameter(self.kappa.clone())
        
        # Forward with gradient blocking
        # Create mask for selective detach (detach where sum > 0)
        detach_mask = (antithesis_param.sum() > 0)  # Keep as boolean
        synthesis, _ = optimized_circular_convolution(
            thesis_param,
            selective_detach(antithesis_param, mask=detach_mask)
        )
        
        loss = synthesis.sum()
        loss.backward()
        
        # Thesis should receive gradients
        assert thesis_param.grad is not None
        
        # Antithesis may or may not receive gradients based on condition
        # (depends on the specific implementation of selective_detach)
    
    def test_memory_efficient_multi_layer_processing(self):
        """Test memory efficiency in multi-layer processing."""
        num_layers = 6
        
        # Simulate processing through multiple DELTA blocks
        x = self.h_in
        for layer in range(num_layers):
            # DELTA-compliant operations: linear projection + stable activation
            weight = torch.randn(self.d_model, self.d_model)
            bias = torch.randn(self.d_model)
            x = torch.matmul(x, weight.T) + bias  # Linear projection
            x = stable_gelu(x)  # Stable activation as per DELTA spec
            
            # DELTA-style processing (no attention needed in this test)
            # Just apply layer normalization as per DELTA Stage 4
            x = stable_layer_norm(x, normalized_shape=(x.shape[-1],))
            
            # Stability check
            x = clip_by_norm(x, max_norm=5.0, dim=-1)
        
        # Final result should be stable and finite
        assert torch.isfinite(x).all()
        assert x.shape == self.h_in.shape
    
    def test_advanced_synthesis_with_all_optimizations(self):
        """Test synthesis with all advanced optimizations enabled."""
        # Use all advanced features
        V_T_stable = stable_layer_norm(self.V_T, normalized_shape=(self.V_T.shape[-1],))
        kappa_stable = stable_layer_norm(self.kappa, normalized_shape=(self.kappa.shape[-1],))
        
        # Multi-scale convolution
        synthesis = multi_scale_fft_convolution(V_T_stable, kappa_stable, scales=[2, 4, 8])
        
        # Numerical stability
        synthesis = clip_by_value(synthesis, -10.0, 10.0)
        synthesis = stable_layer_norm(synthesis, normalized_shape=(synthesis.shape[-1],))
        
        # DELTA Stage 4: Residual connection + LayerNorm (as per specification)
        synthesis = synthesis + self.h_in  # Residual connection
        synthesis = stable_layer_norm(synthesis, normalized_shape=(synthesis.shape[-1],), eps=1e-6)
        
        # Final checks
        assert torch.isfinite(synthesis).all()
        assert check_numerical_stability(synthesis)
        assert synthesis.shape == self.h_in.shape


if __name__ == "__main__":
    pytest.main([__file__])
