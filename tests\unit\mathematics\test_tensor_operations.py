"""
Comprehensive test suite for tensor operations in DELTA architecture.

This module tests utility tensor operations used throughout the DELTA blocks,
including concatenation, masking, reshaping, and other tensor manipulations
specified in the architecture.

Tests follow TDD principles and validate specification adherence.
"""

import pytest
import torch
import numpy as np
from typing import Tuple, Optional, List
import math

from source.delta.math.tensor_ops import (
    safe_concat,
    split_tensor,
    reshape_for_broadcast,
    masked_mean,
    masked_sum,
    normalize_tensor,
    tensor_statistics,
    create_causal_mask,
    apply_rotary_embeddings,
    circular_shift,
    repeat_interleave_batch,
    gather_from_indices,
    scatter_to_indices,
    ensure_contiguous,
    compute_pairwise_distances,
    apply_temperature_scaling,
    batch_diagonal,
    create_additive_causal_mask
)


class TestSafeTensorOperations:
    """Test safe tensor operations for robust processing."""
    
    def setup_method(self):
        """Set up test fixtures."""
        torch.manual_seed(42)
        self.batch_size = 4
        self.seq_len = 8
        self.d_model = 64
        
        self.tensor_a = torch.randn(self.batch_size, self.seq_len, self.d_model)
        self.tensor_b = torch.randn(self.batch_size, self.seq_len, self.d_model)
        self.tensor_c = torch.randn(self.batch_size, self.seq_len, self.d_model // 2)
    
    def test_safe_concat_basic(self):
        """Test basic safe concatenation."""
        result = safe_concat([self.tensor_a, self.tensor_b], dim=-1)
        
        expected_shape = (self.batch_size, self.seq_len, self.d_model * 2)
        assert result.shape == expected_shape
        assert torch.isfinite(result).all()
        
        # Check that concatenation is correct
        torch.testing.assert_close(result[..., :self.d_model], self.tensor_a)
        torch.testing.assert_close(result[..., self.d_model:], self.tensor_b)
    
    def test_safe_concat_different_sizes(self):
        """Test concatenation with different sizes."""
        result = safe_concat([self.tensor_a, self.tensor_c], dim=-1)
        
        expected_shape = (self.batch_size, self.seq_len, self.d_model + self.d_model // 2)
        assert result.shape == expected_shape
        assert torch.isfinite(result).all()
    
    def test_safe_concat_empty_list(self):
        """Test safe concatenation with edge cases."""
        # Empty list
        with pytest.raises((ValueError, IndexError)):
            safe_concat([], dim=-1)
        
        # Single tensor
        result = safe_concat([self.tensor_a], dim=-1)
        torch.testing.assert_close(result, self.tensor_a)
    
    def test_safe_concat_dimension_validation(self):
        """Test that safe concatenation validates dimensions."""
        # Incompatible shapes (different batch size)
        incompatible = torch.randn(self.batch_size + 1, self.seq_len, self.d_model)
        
        with pytest.raises((RuntimeError, ValueError)):
            safe_concat([self.tensor_a, incompatible], dim=-1)
    
    def test_split_tensor_basic(self):
        """Test basic tensor splitting."""
        # Concatenate first, then split
        concatenated = safe_concat([self.tensor_a, self.tensor_b], dim=-1)
        
        split_result = split_tensor(concatenated, [self.d_model, self.d_model], dim=-1)
        
        assert len(split_result) == 2
        torch.testing.assert_close(split_result[0], self.tensor_a)
        torch.testing.assert_close(split_result[1], self.tensor_b)
    
    def test_split_tensor_unequal_sizes(self):
        """Test splitting into unequal sizes."""
        sizes = [self.d_model // 4, self.d_model // 2, self.d_model // 4]
        total_size = sum(sizes)
        
        # Create tensor with appropriate size
        tensor = torch.randn(self.batch_size, self.seq_len, total_size)
        
        split_result = split_tensor(tensor, sizes, dim=-1)
        
        assert len(split_result) == 3
        for i, size in enumerate(sizes):
            assert split_result[i].shape[-1] == size
        
        # Concatenating back should give original
        reconstructed = safe_concat(split_result, dim=-1)
        torch.testing.assert_close(reconstructed, tensor)
    
    def test_split_tensor_size_validation(self):
        """Test that splitting validates sizes."""
        # Sizes don't match tensor dimension
        with pytest.raises((ValueError, RuntimeError)):
            split_tensor(self.tensor_a, [self.d_model + 1], dim=-1)
        
        # Sizes sum to more than tensor dimension
        with pytest.raises((ValueError, RuntimeError)):
            split_tensor(self.tensor_a, [self.d_model // 2, self.d_model], dim=-1)


class TestReshapeOperations:
    """Test tensor reshaping and broadcasting operations."""
    
    def setup_method(self):
        """Set up test fixtures."""
        torch.manual_seed(42)
        self.batch_size = 4
        self.seq_len = 8
        self.d_model = 64
        
        self.tensor_2d = torch.randn(self.batch_size, self.d_model)
        self.tensor_3d = torch.randn(self.batch_size, self.seq_len, self.d_model)
    
    def test_reshape_for_broadcast_basic(self):
        """Test basic reshape for broadcasting."""
        # Reshape 2D to be broadcastable with 3D
        reshaped = reshape_for_broadcast(self.tensor_2d, target_shape=self.tensor_3d.shape)
        
        # Should be able to broadcast
        result = reshaped + self.tensor_3d
        assert result.shape == self.tensor_3d.shape
        assert torch.isfinite(result).all()
    
    def test_reshape_for_broadcast_dimensions(self):
        """Test reshape with different target dimensions."""
        # Target: (batch, seq, d_model)
        # Source: (batch, d_model)
        target_shape = (self.batch_size, self.seq_len, self.d_model)
        reshaped = reshape_for_broadcast(self.tensor_2d, target_shape=target_shape)
        
        # Should add dimension for sequence length
        expected_shape = (self.batch_size, 1, self.d_model)
        assert reshaped.shape == expected_shape
        
        # Should broadcast correctly
        broadcasted = reshaped.expand(target_shape)
        assert broadcasted.shape == target_shape
    
    def test_reshape_for_broadcast_edge_cases(self):
        """Test reshape edge cases."""
        # Same shape - should return unchanged
        reshaped = reshape_for_broadcast(self.tensor_3d, target_shape=self.tensor_3d.shape)
        torch.testing.assert_close(reshaped, self.tensor_3d)
        
        # Single element tensor
        single = torch.tensor(1.0)
        reshaped_single = reshape_for_broadcast(single, target_shape=self.tensor_3d.shape)
        
        # Should be broadcastable
        result = reshaped_single + self.tensor_3d
        assert result.shape == self.tensor_3d.shape
    
    def test_ensure_contiguous(self):
        """Test ensuring tensor contiguity."""
        # Create non-contiguous tensor
        non_contiguous = self.tensor_3d.transpose(1, 2)
        assert not non_contiguous.is_contiguous()
        
        # Make contiguous
        contiguous = ensure_contiguous(non_contiguous)
        assert contiguous.is_contiguous()
        
        # Values should be preserved
        torch.testing.assert_close(
            contiguous.transpose(1, 2), self.tensor_3d
        )
    
    def test_ensure_contiguous_already_contiguous(self):
        """Test ensure_contiguous with already contiguous tensor."""
        # Should be no-op for already contiguous tensors
        result = ensure_contiguous(self.tensor_3d)
        
        # Should be the same object (no copy needed)
        assert result is self.tensor_3d
        assert result.is_contiguous()


class TestMaskedOperations:
    """Test masked tensor operations for variable-length sequences."""
    
    def setup_method(self):
        """Set up test fixtures."""
        torch.manual_seed(42)
        self.batch_size = 4
        self.seq_len = 8
        self.d_model = 64
        
        self.tensor = torch.randn(self.batch_size, self.seq_len, self.d_model)
        
        # Create masks for variable-length sequences
        self.lengths = torch.tensor([6, 8, 4, 7])  # Different lengths per batch
        self.mask = torch.zeros(self.batch_size, self.seq_len, dtype=torch.bool)
        for i, length in enumerate(self.lengths):
            self.mask[i, :length] = True
    
    def test_masked_mean(self):
        """Test masked mean calculation."""
        result = masked_mean(self.tensor, self.mask, dim=1)
        
        # Result should have shape (batch_size, d_model)
        assert result.shape == (self.batch_size, self.d_model)
        assert torch.isfinite(result).all()
        
        # Manually verify for first batch element
        first_batch = self.tensor[0, :self.lengths[0], :]
        expected_mean = first_batch.mean(dim=0)
        torch.testing.assert_close(result[0], expected_mean, rtol=1e-5, atol=1e-6)
    
    def test_masked_sum(self):
        """Test masked sum calculation."""
        result = masked_sum(self.tensor, self.mask, dim=1)
        
        # Result should have shape (batch_size, d_model)
        assert result.shape == (self.batch_size, self.d_model)
        assert torch.isfinite(result).all()
        
        # Manually verify for first batch element
        first_batch = self.tensor[0, :self.lengths[0], :]
        expected_sum = first_batch.sum(dim=0)
        torch.testing.assert_close(result[0], expected_sum, rtol=1e-5, atol=1e-6)
    
    def test_masked_operations_all_masked(self):
        """Test masked operations when all elements are masked."""
        all_masked = torch.zeros_like(self.mask)
        
        # Should handle gracefully
        mean_result = masked_mean(self.tensor, all_masked, dim=1)
        sum_result = masked_sum(self.tensor, all_masked, dim=1)
        
        # Results should be zero (or NaN for mean, depending on implementation)
        assert torch.isfinite(sum_result).all()
        assert torch.allclose(sum_result, torch.zeros_like(sum_result))
    
    def test_masked_operations_different_dimensions(self):
        """Test masked operations along different dimensions."""
        # Mask along feature dimension
        feature_mask = torch.ones(self.batch_size, self.seq_len, self.d_model, dtype=torch.bool)
        feature_mask[:, :, self.d_model//2:] = False  # Mask half the features
        
        result = masked_mean(self.tensor, feature_mask, dim=-1)
        
        # Should average only over unmasked features
        assert result.shape == (self.batch_size, self.seq_len)
        assert torch.isfinite(result).all()


class TestCausalMasks:
    """Test causal mask creation and application."""
    
    def setup_method(self):
        """Set up test fixtures."""
        torch.manual_seed(42)
        self.seq_len = 8
        self.batch_size = 4
    
    def test_create_causal_mask(self):
        """Test causal mask creation."""
        device = torch.device('cpu')
        mask = create_causal_mask(self.seq_len, device)
        
        # Should be lower triangular (including diagonal)
        assert mask.shape == (self.seq_len, self.seq_len)
        
        # Check causal property: mask[i, j] should be True if j <= i
        for i in range(self.seq_len):
            for j in range(self.seq_len):
                if j <= i:
                    assert mask[i, j] == True
                else:
                    assert mask[i, j] == False
    
    def test_create_additive_causal_mask(self):
        """Test additive causal mask creation."""
        device = torch.device('cpu')
        mask = create_additive_causal_mask(self.seq_len, device)
        
        assert mask.shape == (self.seq_len, self.seq_len)
        
        # Upper triangular part should be -inf (or very negative)
        for i in range(self.seq_len):
            for j in range(i + 1, self.seq_len):
                assert mask[i, j] < -1e6  # Very negative value
            
            # Lower triangular and diagonal should be 0
            for j in range(i + 1):
                assert mask[i, j] == 0.0
    
    def test_causal_mask_attention_application(self):
        """Test applying causal mask to attention scores."""
        # Create attention scores
        scores = torch.randn(self.batch_size, self.seq_len, self.seq_len)
        
        # Apply causal mask
        device = torch.device('cpu')
        additive_mask = create_additive_causal_mask(self.seq_len, device)
        masked_scores = scores + additive_mask
        
        # Apply softmax
        attention_weights = torch.softmax(masked_scores, dim=-1)
        
        # Check that future positions have zero attention
        for i in range(self.seq_len):
            for j in range(i + 1, self.seq_len):
                assert torch.allclose(
                    attention_weights[:, i, j],
                    torch.zeros_like(attention_weights[:, i, j]),
                    atol=1e-6
                )
    
    def test_causal_mask_different_devices(self):
        """Test causal mask creation on different devices."""
        # Test CPU
        cpu_mask = create_causal_mask(self.seq_len, device=torch.device('cpu'))
        assert cpu_mask.device == torch.device('cpu')
        
        # Test different dtypes
        device = torch.device('cpu')
        float_mask = create_causal_mask(self.seq_len, device, dtype=torch.float32)
        assert float_mask.dtype == torch.float32
        
        bool_mask = create_causal_mask(self.seq_len, device, dtype=torch.bool)
        assert bool_mask.dtype == torch.bool


class TestPositionalOperations:
    """Test positional operations like rotary embeddings and circular shifts."""
    
    def setup_method(self):
        """Set up test fixtures."""
        torch.manual_seed(42)
        self.batch_size = 4
        self.seq_len = 8
        self.d_model = 64
        
        self.tensor = torch.randn(self.batch_size, self.seq_len, self.d_model)
        self.positions = torch.arange(self.seq_len).unsqueeze(0).expand(self.batch_size, -1)
    
    def test_apply_rotary_embeddings(self):
        """Test application of rotary positional embeddings."""
        result = apply_rotary_embeddings(self.tensor, self.positions, dim=self.d_model)
        
        # Shape should be preserved
        assert result.shape == self.tensor.shape
        assert torch.isfinite(result).all()
        
        # Different positions should produce different results
        pos_0 = apply_rotary_embeddings(self.tensor[:, :1, :], torch.zeros(self.batch_size, 1, dtype=torch.long), dim=self.d_model)
        pos_1 = apply_rotary_embeddings(self.tensor[:, :1, :], torch.ones(self.batch_size, 1, dtype=torch.long), dim=self.d_model)
        
        assert not torch.allclose(pos_0, pos_1, rtol=1e-3)
    
    def test_rotary_embeddings_properties(self):
        """Test mathematical properties of rotary embeddings."""
        # Rotary embeddings should preserve magnitude
        original_norms = torch.norm(self.tensor, dim=-1)
        embedded = apply_rotary_embeddings(self.tensor, self.positions, dim=self.d_model)
        embedded_norms = torch.norm(embedded, dim=-1)
        
        # Norms should be approximately preserved
        torch.testing.assert_close(original_norms, embedded_norms, rtol=1e-4, atol=1e-5)
    
    def test_circular_shift(self):
        """Test circular shifting of tensors."""
        shift_amounts = [1, -1, 3, -3]
        
        for shift in shift_amounts:
            shifted = circular_shift(self.tensor, shift, dims=1)
            
            # Shape should be preserved
            assert shifted.shape == self.tensor.shape
            assert torch.isfinite(shifted).all()
            
            # Verify circular property
            if shift > 0:
                # Elements should have moved to the right
                torch.testing.assert_close(
                    shifted[:, shift:, :], self.tensor[:, :-shift, :]
                )
                torch.testing.assert_close(
                    shifted[:, :shift, :], self.tensor[:, -shift:, :]
                )
    
    def test_circular_shift_edge_cases(self):
        """Test circular shift edge cases."""
        # Zero shift
        no_shift = circular_shift(self.tensor, 0, dims=1)
        torch.testing.assert_close(no_shift, self.tensor)
        
        # Shift by sequence length (should be identity)
        full_shift = circular_shift(self.tensor, self.seq_len, dims=1)
        torch.testing.assert_close(full_shift, self.tensor)
        
        # Negative shift by sequence length
        neg_full_shift = circular_shift(self.tensor, -self.seq_len, dims=1)
        torch.testing.assert_close(neg_full_shift, self.tensor)


class TestIndexingOperations:
    """Test indexing and gathering operations."""
    
    def setup_method(self):
        """Set up test fixtures."""
        torch.manual_seed(42)
        self.batch_size = 4
        self.seq_len = 8
        self.d_model = 64
        
        self.tensor = torch.randn(self.batch_size, self.seq_len, self.d_model)
        self.indices = torch.randint(0, self.seq_len, (self.batch_size, 4))  # Gather 4 elements
    
    def test_gather_from_indices(self):
        """Test gathering elements from specified indices."""
        result = gather_from_indices(self.tensor, self.indices, dim=1)
        
        # Shape should be (batch_size, 4, d_model)
        expected_shape = (self.batch_size, 4, self.d_model)
        assert result.shape == expected_shape
        assert torch.isfinite(result).all()
        
        # Manually verify for first batch element
        for i in range(4):
            idx = self.indices[0, i]
            expected = self.tensor[0, idx, :]
            torch.testing.assert_close(result[0, i, :], expected)
    
    def test_scatter_to_indices(self):
        """Test scattering values to specified indices."""
        # Use a completely fresh seed to ensure isolation
        torch.manual_seed(12345)
        
        # Create test data from scratch 
        batch_size = 2  # Smaller for simpler debugging
        seq_len = 6
        d_model = 4
        
        # Create simple, predictable indices without duplicates
        indices = torch.tensor([[0, 2, 4, 5], [1, 3, 0, 2]])  # No duplicates
        values = torch.arange(batch_size * 4 * d_model, dtype=torch.float32).view(batch_size, 4, d_model)
        output = torch.zeros(batch_size, seq_len, d_model)
        
        result = scatter_to_indices(output, indices, values, dim=1)
        
        assert result.shape == (batch_size, seq_len, d_model)
        assert torch.isfinite(result).all()
        
        # Verify scattered values
        for b in range(batch_size):
            for i in range(4):
                idx = indices[b, i].item()
                expected = values[b, i, :]
                actual = result[b, idx, :]
                torch.testing.assert_close(actual, expected, 
                                         msg=f"Batch {b}, position {i} -> index {idx}")
        
        # Verify that only specified indices are non-zero
        for b in range(batch_size):
            for seq_idx in range(seq_len):
                if seq_idx in indices[b]:
                    assert (result[b, seq_idx, :] != 0).any(), f"Index {seq_idx} should be non-zero"
                else:
                    assert (result[b, seq_idx, :] == 0).all(), f"Index {seq_idx} should be zero"
    
    def test_gather_scatter_roundtrip(self):
        """Test that gather and scatter are inverse operations."""
        # Gather some elements
        gathered = gather_from_indices(self.tensor, self.indices, dim=1)
        
        # Create empty output and scatter back
        output = torch.zeros_like(self.tensor)
        scattered = scatter_to_indices(output, self.indices, gathered, dim=1)
        
        # The scattered values should match original at those indices
        for b in range(self.batch_size):
            for i in range(4):
                idx = self.indices[b, i]
                torch.testing.assert_close(
                    scattered[b, idx, :], self.tensor[b, idx, :]
                )
    
    def test_repeat_interleave_batch(self):
        """Test batch dimension repeat interleaving."""
        repeats = 3
        result = repeat_interleave_batch(self.tensor, repeats)
        
        # Shape should be (batch_size * repeats, seq_len, d_model)
        expected_shape = (self.batch_size * repeats, self.seq_len, self.d_model)
        assert result.shape == expected_shape
        
        # Verify repetition pattern
        for i in range(self.batch_size):
            for r in range(repeats):
                idx = i * repeats + r
                torch.testing.assert_close(result[idx], self.tensor[i])


class TestStatisticalOperations:
    """Test statistical and analytical tensor operations."""
    
    def setup_method(self):
        """Set up test fixtures."""
        torch.manual_seed(42)
        self.batch_size = 4
        self.seq_len = 8
        self.d_model = 64
        
        self.tensor = torch.randn(self.batch_size, self.seq_len, self.d_model)
    
    def test_normalize_tensor(self):
        """Test tensor normalization."""
        # L2 normalization
        normalized_l2 = normalize_tensor(self.tensor, p=2, dim=-1)
        
        # Should preserve shape
        assert normalized_l2.shape == self.tensor.shape
        assert torch.isfinite(normalized_l2).all()
        
        # Should have unit L2 norm
        norms = torch.norm(normalized_l2, p=2, dim=-1)
        expected_norms = torch.ones_like(norms)
        torch.testing.assert_close(norms, expected_norms, rtol=1e-6, atol=1e-5)
        
        # L1 normalization
        normalized_l1 = normalize_tensor(self.tensor, p=1, dim=-1)
        l1_norms = torch.norm(normalized_l1, p=1, dim=-1)
        torch.testing.assert_close(l1_norms, expected_norms, rtol=1e-6, atol=1e-5)
    
    def test_tensor_statistics(self):
        """Test tensor statistics computation."""
        stats = tensor_statistics(self.tensor, dim=-1)
        
        # Should contain mean, var, std, min, max
        required_keys = ['mean', 'var', 'std', 'min', 'max']
        for key in required_keys:
            assert key in stats
            assert stats[key].shape == (self.batch_size, self.seq_len)
            assert torch.isfinite(stats[key]).all()
        
        # Verify some statistical properties
        assert (stats['var'] >= 0).all()  # Variance is non-negative
        assert (stats['std'] >= 0).all()  # Standard deviation is non-negative
        assert (stats['min'] <= stats['max']).all()  # Min <= Max
        
        # Verify mean calculation
        expected_mean = self.tensor.mean(dim=-1)
        torch.testing.assert_close(stats['mean'], expected_mean, rtol=1e-5, atol=1e-6)
    
    def test_compute_pairwise_distances(self):
        """Test pairwise distance computation."""
        # Use smaller tensors for efficiency
        tensor_a = torch.randn(4, 16)
        tensor_b = torch.randn(6, 16)
        
        distances = compute_pairwise_distances(tensor_a, tensor_b)
        
        # Shape should be (4, 6)
        assert distances.shape == (4, 6)
        assert torch.isfinite(distances).all()
        assert (distances >= 0).all()  # Distances are non-negative
        
        # Verify distance calculation
        expected_dist = torch.norm(tensor_a[0] - tensor_b[0])
        torch.testing.assert_close(distances[0, 0], expected_dist, rtol=1e-5, atol=1e-6)
    
    def test_compute_pairwise_distances_same_tensor(self):
        """Test pairwise distances with same tensor."""
        tensor = torch.randn(5, 16)
        distances = compute_pairwise_distances(tensor, tensor)
        
        # Diagonal should be zero (distance from point to itself)
        diagonal = torch.diagonal(distances)
        torch.testing.assert_close(diagonal, torch.zeros_like(diagonal), rtol=1e-7, atol=1e-6)
        
        # Matrix should be symmetric
        torch.testing.assert_close(distances, distances.T, rtol=1e-5, atol=1e-6)
    
    def test_apply_temperature_scaling(self):
        """Test temperature scaling application."""
        temperatures = [0.1, 1.0, 10.0]
        
        for temp in temperatures:
            scaled = apply_temperature_scaling(self.tensor, temperature=temp)
            
            # Shape should be preserved
            assert scaled.shape == self.tensor.shape
            assert torch.isfinite(scaled).all()
            
            # Verify scaling
            expected = self.tensor / temp
            torch.testing.assert_close(scaled, expected, rtol=1e-6, atol=1e-7)
    
    def test_batch_diagonal(self):
        """Test batch diagonal extraction."""
        # Create batch of square matrices
        batch_matrices = torch.randn(self.batch_size, self.seq_len, self.seq_len)
        
        diagonals = batch_diagonal(batch_matrices)
        
        # Shape should be (batch_size, seq_len)
        assert diagonals.shape == (self.batch_size, self.seq_len)
        assert torch.isfinite(diagonals).all()
        
        # Verify diagonal extraction
        for i in range(self.batch_size):
            expected_diag = torch.diagonal(batch_matrices[i])
            torch.testing.assert_close(diagonals[i], expected_diag, rtol=1e-6, atol=1e-7)


class TestTensorOperationsIntegration:
    """Integration tests for tensor operations in DELTA context."""
    
    def setup_method(self):
        """Set up test fixtures for integration testing."""
        torch.manual_seed(42)
        self.batch_size = 4
        self.seq_len = 16
        self.d_model = 128
        
        # Simulate DELTA block components
        self.V_T = torch.randn(self.batch_size, self.d_model)      # Thesis
        self.kappa_new = torch.randn(self.batch_size, self.d_model)   # New antithesis
        self.kappa_context = torch.randn(self.batch_size, self.d_model)  # Context from cache
        self.h_in = torch.randn(self.batch_size, self.d_model)     # Input state
    
    def test_effective_antithesis_fusion(self):
        """Test effective antithesis fusion as specified in DELTA."""
        # κ_effective = W_κ[κ_new; κ_context] + b_κ
        
        # Concatenate new and context antithesis
        fused_input = safe_concat([self.kappa_new, self.kappa_context], dim=-1)
        assert fused_input.shape == (self.batch_size, 2 * self.d_model)
        
        # Apply linear transformation (simulated)
        W_kappa = torch.randn(2 * self.d_model, self.d_model)
        b_kappa = torch.randn(self.d_model)
        
        kappa_effective = torch.matmul(fused_input, W_kappa) + b_kappa
        assert kappa_effective.shape == (self.batch_size, self.d_model)
        assert torch.isfinite(kappa_effective).all()
    
    def test_gru_gate_computation(self):
        """Test GRU-like gating as used in DELTA Stage 4."""
        # z = σ(W_z [V_S; h_in] + b_z)
        # h_out = (1-z) ⊙ h_in + z ⊙ V_S
        
        V_S = torch.randn(self.batch_size, self.d_model)  # Synthesis result
        
        # Concatenate for gating
        gate_input = safe_concat([V_S, self.h_in], dim=-1)
        assert gate_input.shape == (self.batch_size, 2 * self.d_model)
        
        # Compute gate
        W_z = torch.randn(2 * self.d_model, self.d_model)
        b_z = torch.randn(self.d_model)
        
        z = torch.sigmoid(torch.matmul(gate_input, W_z) + b_z)
        assert z.shape == (self.batch_size, self.d_model)
        assert (z >= 0).all() and (z <= 1).all()  # Valid gate values
        
        # Apply gate
        h_out = (1 - z) * self.h_in + z * V_S
        assert h_out.shape == (self.batch_size, self.d_model)
        assert torch.isfinite(h_out).all()
    
    def test_sequence_processing_with_masks(self):
        """Test sequence processing with variable-length inputs."""
        # Simulate variable-length sequences
        seq_lengths = torch.tensor([12, 16, 8, 14])
        max_len = seq_lengths.max().item()
        
        # Create padded sequences
        sequences = torch.randn(self.batch_size, max_len, self.d_model)
        
        # Create attention masks
        mask = torch.zeros(self.batch_size, max_len, dtype=torch.bool)
        for i, length in enumerate(seq_lengths):
            mask[i, :length] = True
        
        # Process with masking
        masked_mean_result = masked_mean(sequences, mask, dim=1)
        assert masked_mean_result.shape == (self.batch_size, self.d_model)
        assert torch.isfinite(masked_mean_result).all()
        
        # Apply causal masking for attention
        device = torch.device('cpu')
        causal_mask = create_causal_mask(max_len, device)
        assert causal_mask.shape == (max_len, max_len)
        
        # Combine with sequence mask
        combined_mask = mask.unsqueeze(1) & mask.unsqueeze(2) & causal_mask.unsqueeze(0)
        assert combined_mask.shape == (self.batch_size, max_len, max_len)
    
    def test_multi_scale_tensor_operations(self):
        """Test tensor operations at different scales."""
        scales = [32, 64, 128, 256]
        
        for scale in scales:
            tensor = torch.randn(self.batch_size, scale)
            
            # Normalize
            normalized = normalize_tensor(tensor, p=2, dim=-1)
            assert normalized.shape == tensor.shape
            
            # Split and concatenate
            if scale % 4 == 0:  # Ensure divisible
                split_size = scale // 4
                splits = split_tensor(tensor, [split_size] * 4, dim=-1)
                reconstructed = safe_concat(splits, dim=-1)
                torch.testing.assert_close(reconstructed, tensor)
            
            # Gather and scatter
            indices = torch.randint(0, min(scale, 8), (self.batch_size, 4))
            if tensor.dim() == 2:
                # Need to add sequence dimension for gather/scatter
                tensor_3d = tensor.unsqueeze(1)  # (batch_size, 1, scale)
                # For gathering from last dim, indices should be (batch_size, 1, 4)
                gathered = gather_from_indices(tensor_3d, indices.unsqueeze(1), dim=-1)
                assert gathered.shape[0] == self.batch_size
                assert gathered.shape == (self.batch_size, 1, 4)
    
    def test_numerical_stability_edge_cases(self):
        """Test numerical stability with edge cases."""
        edge_cases = [
            torch.zeros(self.batch_size, self.d_model),           # All zeros
            torch.ones(self.batch_size, self.d_model) * 1e-8,    # Very small
            torch.ones(self.batch_size, self.d_model) * 1e8,     # Very large
            torch.randn(self.batch_size, self.d_model) * 1e-4,   # Small random
        ]
        
        for tensor in edge_cases:
            # Test normalization
            normalized = normalize_tensor(tensor, p=2, dim=-1)
            assert torch.isfinite(normalized).all()
            
            # Test statistics
            stats = tensor_statistics(tensor, dim=-1)
            for key in stats:
                assert torch.isfinite(stats[key]).all()
            
            # Test safe concatenation
            concatenated = safe_concat([tensor, tensor], dim=-1)
            assert torch.isfinite(concatenated).all()
            assert concatenated.shape == (self.batch_size, 2 * self.d_model)
    
    def test_gradient_flow_through_operations(self):
        """Test gradient flow through complex tensor operations."""
        # Create tensors requiring gradients
        tensor_a = torch.randn(self.batch_size, self.d_model, requires_grad=True)
        tensor_b = torch.randn(self.batch_size, self.d_model, requires_grad=True)
        
        # Chain of operations
        concatenated = safe_concat([tensor_a, tensor_b], dim=-1)
        splits = split_tensor(concatenated, [self.d_model, self.d_model], dim=-1)
        normalized_a = normalize_tensor(splits[0], p=2, dim=-1)
        normalized_b = normalize_tensor(splits[1], p=2, dim=-1)
        
        # Final loss
        loss = (normalized_a + normalized_b).sum()
        loss.backward()
        
        # Check gradients
        assert tensor_a.grad is not None
        assert tensor_b.grad is not None
        assert torch.isfinite(tensor_a.grad).all()
        assert torch.isfinite(tensor_b.grad).all()
    
    def test_memory_efficiency(self):
        """Test memory efficiency of tensor operations."""
        # Test with larger tensors to check memory usage
        large_tensor = torch.randn(8, 1024, 512)
        
        # Operations should not cause memory explosion
        normalized = normalize_tensor(large_tensor, p=2, dim=-1)
        assert normalized.shape == large_tensor.shape
        
        # In-place operations where possible
        large_tensor_copy = large_tensor.clone()
        large_tensor_copy = ensure_contiguous(large_tensor_copy)
        assert large_tensor_copy.is_contiguous()
        
        # Clean up
        del large_tensor, normalized, large_tensor_copy


if __name__ == "__main__":
    pytest.main([__file__])
