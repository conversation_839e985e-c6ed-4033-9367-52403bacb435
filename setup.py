# Setup configuration for DELTA package
from setuptools import setup, find_packages

with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

with open("requirements.txt", "r", encoding="utf-8") as fh:
    requirements = [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="delta-decoder",
    version="0.1.0",
    author="DELTA Research Team",
    author_email="<EMAIL>",
    description="DELTA: A Two-Phase Dialectic Decoder Architecture with κ-Cache for Robust Logical Reasoning",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/CursiveCrow/delta",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 3 - Alpha",
        "Intended Audience :: Developers",
        "Intended Audience :: Science/Research",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Programming Language :: Python :: 3.13",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Software Development :: Libraries :: Python Modules",
    ],
    python_requires=">=3.10",
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=7.0.0",
            "black>=22.0.0",
            "isort>=5.10.0",
            "mypy>=0.950",
            "flake8>=4.0.0",
        ],
        "jupyter": [
            "jupyter>=1.0.0",
            "matplotlib>=3.5.0",
            "seaborn>=0.11.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "delta-train=scripts.train:main",
            "delta-inference=scripts.inference:main",
            "delta-evaluate=scripts.evaluate:main",
            "delta-preprocess=scripts.preprocess_data:main",
            "delta-export=scripts.export_model:main",
        ],
    },
)
