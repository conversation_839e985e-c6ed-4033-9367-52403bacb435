# Modern Python packaging configuration for DELTA
[build-system]
requires = ["setuptools>=45", "wheel", "setuptools_scm[toml]>=6.2"]
build-backend = "setuptools.build_meta"

[project]
name = "delta-decoder"
version = "0.1.0"
description = "DELTA: A Two-Phase Dialectic Decoder Architecture with κ-Cache for Robust Logical Reasoning"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "DELTA Research Team", email = "<EMAIL>"},
]
keywords = ["artificial intelligence", "language models", "reasoning", "dialectics", "neural networks"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "Intended Audience :: Science/Research",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3.13",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
]
requires-python = ">=3.10"
dependencies = [
    "torch>=2.0.0",
    "numpy>=1.21.0",
    "scipy>=1.7.0",
    "transformers>=4.20.0",
    "datasets>=2.0.0",
    "tensorboard>=2.8.0",
    "tqdm>=4.64.0",
    "click>=8.0.0",
    "pydantic>=1.9.0",
    "omegaconf>=2.2.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "black>=22.0.0",
    "isort>=5.10.0",
    "mypy>=0.950",
    "flake8>=4.0.0",
]
experiment = [
    "wandb>=0.12.0",
    "hydra-core>=1.2.0",
    "jupyter>=1.0.0",
    "matplotlib>=3.5.0",
    "seaborn>=0.11.0",
]

[project.scripts]
delta-train = "scripts.train:main"
delta-inference = "scripts.inference:main"
delta-evaluate = "scripts.evaluate:main"
delta-preprocess = "scripts.preprocess_data:main"
delta-export = "scripts.export_model:main"

[project.urls]
Homepage = "https://github.com/CursiveCrow/delta"
Repository = "https://github.com/CursiveCrow/delta"
Documentation = "https://delta-decoder.readthedocs.io/"
"Bug Tracker" = "https://github.com/CursiveCrow/delta/issues"

[tool.setuptools.packages.find]
where = ["."]
include = ["source*", "data*", "scripts*"]

[tool.black]
line-length = 88
target-version = ['py310', 'py311', 'py312', 'py313']

[tool.isort]
profile = "black"
multi_line_output = 3

[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
