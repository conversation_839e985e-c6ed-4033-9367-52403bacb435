"""Math utilities and common mathematical operations for DELTA.

This module provides mathematical operations essential for the DELTA
architecture, including FFT-based synthesis, attention mechanisms,
activations, normalizations, and numerical stability utilities.
"""

# Core FFT operations for synthesis
from .fft_ops import (
    fft_magnitude_phase,
    apply_frequency_mask,
    frequency_domain_attention,
    spectral_regularization,
    adaptive_frequency_filtering,
    ensure_numerical_stability
)

# Convolution operations
from .convolution import (
    circular_convolve,
    LearnableConvolution
)

# Note: Feed-forward networks moved to components/ folder

# Note: Attention mechanisms moved to components/ folder

# Activation functions
from .activations import (
    gelu,
    swish,
    mish,
    gated_linear_unit,
    swiglu,
    geglu,
    adaptive_activation,
    smooth_relu,
    hard_swish,
    hard_sigmoid,
    prelu_custom,
    elu_plus,
    softmax_temperature,
    sparsemax,
    gumbel_softmax,
    AdaptiveActivation,
    MixtureOfActivations,
    get_activation_fn
)

# Note: Normalization operations moved to components/ folder

# Tensor operations
from .tensor_ops import (
    safe_concat,
    split_tensor,
    reshape_for_broadcast,
    masked_mean,
    masked_sum,
    normalize_tensor,
    tensor_statistics,
    create_causal_mask,
    apply_rotary_embeddings,
    circular_shift,
    repeat_interleave_batch,
    gather_from_indices,
    scatter_to_indices,
    ensure_contiguous,
    compute_pairwise_distances,
    apply_temperature_scaling,
    batch_diagonal
)

# Advanced operations - temporarily commented out due to missing modules
# from .advanced.circular_convolution import (
#     multi_scale_fft_convolution,
#     adaptive_fft_convolution,
#     grouped_fft_convolution,
#     dilated_fft_convolution,
#     spectral_gated_convolution,
#     HierarchicalFFTConvolution,
#     fast_hadamard_product,
#     circular_convolution_2d,
#     optimized_circular_convolution
# )

from .advanced.gradient_blocking import (
    detach_tensor,
    selective_detach,
    gradient_reversal,
    scale_gradient,
    stop_gradient,
    conditional_detach,
    DualPathGradient,
    gradient_checkpoint,
    GradientClipping,
    create_gradient_mask,
    asymmetric_operation,
    dual_objective_update,
    GradientModulation
)

from .advanced.numerical_stability import (
    safe_log,
    safe_sqrt,
    safe_divide,
    safe_norm,
    clip_by_value,
    clip_by_norm,
    stable_softmax,
    stable_log_softmax,
    prevent_gradient_explosion,
    stable_layer_norm,
    stable_gelu,
    stable_sigmoid,
    stable_tanh,
    NumericalStabilizer,
    check_numerical_stability,
    compute_bounded_attention_scores,
    stabilize_gradients,
    stable_cross_entropy
)

# tensor_optimizations module removed - not part of DELTA specification

__all__ = [
    # FFT operations
    'fft_magnitude_phase',
    'apply_frequency_mask',
    'frequency_domain_attention',
    'spectral_regularization',
    'adaptive_frequency_filtering',

    # Convolution operations
    'circular_convolve',
    'LearnableConvolution',

    # Note: Feed-forward networks moved to components/ folder
    
    # Note: Attention moved to components/ folder
    
    # Activations
    'gelu',
    'swish',
    'mish',
    'gated_linear_unit',
    'swiglu',
    'geglu',
    'adaptive_activation',
    'smooth_relu',
    'hard_swish',
    'hard_sigmoid',
    'prelu_custom',
    'elu_plus',
    'softmax_temperature',
    'sparsemax',
    'gumbel_softmax',
    'AdaptiveActivation',
    'MixtureOfActivations',
    'get_activation_fn',
    
    # Note: Normalization moved to components/ folder
    
    # Tensor operations
    'safe_concat',
    'split_tensor',
    'reshape_for_broadcast',
    'masked_mean',
    'masked_sum',
    'normalize_tensor',
    'tensor_statistics',
    'create_causal_mask',
    'apply_rotary_embeddings',
    'circular_shift',
    'repeat_interleave_batch',
    'gather_from_indices',
    'scatter_to_indices',
    'ensure_contiguous',
    'compute_pairwise_distances',
    'apply_temperature_scaling',
    'batch_diagonal',
    
    # Advanced circular convolution - temporarily commented out
    # 'multi_scale_fft_convolution',
    # 'adaptive_fft_convolution',
    # 'grouped_fft_convolution',
    # 'dilated_fft_convolution',
    # 'spectral_gated_convolution',
    # 'HierarchicalFFTConvolution',
    # 'fast_hadamard_product',
    # 'circular_convolution_2d',
    # 'optimized_circular_convolution',
    
    # Gradient blocking
    'detach_tensor',
    'selective_detach',
    'gradient_reversal',
    'scale_gradient',
    'stop_gradient',
    'conditional_detach',
    'DualPathGradient',
    'gradient_checkpoint',
    'GradientClipping',
    'create_gradient_mask',
    'asymmetric_operation',
    'dual_objective_update',
    'GradientModulation',
    
    # Numerical stability
    'safe_log',
    'safe_sqrt',
    'safe_divide',
    'safe_norm',
    'clip_by_value',
    'clip_by_norm',
    'stable_softmax',
    'stable_log_softmax',
    'prevent_gradient_explosion',
    'stable_layer_norm',
    'stable_gelu',
    'stable_sigmoid',
    'stable_tanh',
    'NumericalStabilizer',
    'check_numerical_stability',
    'compute_bounded_attention_scores',
    'stabilize_gradients',
    'stable_cross_entropy',
    
    # Tensor optimizations removed - not part of DELTA specification
]
