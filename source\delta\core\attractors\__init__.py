"""Antithesis generation components for DELTA architecture.

This module contains all components involved in Stage 2 of the DELTA block:
the generation of constraint forces through structural and semantic attractors.
"""

from .gating_networks import (
    StructuralGatingNetwork,
    SemanticGatingNetwork,
    DualGatingNetwork
)

from .structural_attractors import (
    StructuralAttractor,
    StructuralAttractorBank
)

from .semantic_attractors import (
    GRURecurrentCell,
    SemanticAttractor,
    SemanticAttractorBank
)

from .operator import (
    LowRankOperatorAttractor
)

# Note: CompositeAntithesisGenerator is now internal to DELTABlock
# and not exposed as a public API

__all__ = [
    # Gating networks
    'StructuralGatingNetwork',
    'SemanticGatingNetwork',
    'DualGatingNetwork',

    # Structural attractors
    'StructuralAttractor',
    'StructuralAttractorBank',

    # Semantic attractors
    'GRURecurrentCell',
    'SemanticAttractor',
    'SemanticAttractorBank'
]
