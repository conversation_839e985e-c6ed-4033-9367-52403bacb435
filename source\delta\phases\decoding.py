"""Decoding Phase Implementation

This module implements the Decoding Phase as described in Section 4.2 of the DELTA specification.
The Decoding Phase performs autoregressive generation using the κ-Cache populated during prefill.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import List, Optional, Tuple, Dict, Callable
import logging

from ..core.base.types import DELT<PERSON>onfig, KappaCache, PhaseOutput
from ..core.base.abstract_implementations import AbstractDecodePhase
from ..core.delta_stack import DELTAStack, DELTAStackOutput
from ..math.advanced.numerical_stability import ensure_numerical_stability, stable_softmax

logger = logging.getLogger(__name__)


class DecodingPhase(AbstractDecodePhase):
    """
    Implements the Decoding Phase (§4.2) - "The Execution"
    
    This phase performs autoregressive generation token by token, using the κ-Cache
    populated during the Prefill Phase for contextual constraint retrieval.
    
    Key characteristics:
    - Autoregressive loop generating response token by token
    - Uses κ-Cache attention for retrieving prompt context constraints
    - Linear-time attention complexity O(L) over prompt length (not total sequence)
    - Efficient fusion of new antithesis with cached context
    """
    
    def __init__(
        self, 
        config: DELTAConfig, 
        delta_stack: DELTAStack,
        language_model_head: nn.Module,
        tokenizer: Optional[object] = None
    ):
        """Initialize Decoding Phase.
        
        Args:
            config: DELTA model configuration
            delta_stack: The stack of DELTA blocks
            language_model_head: Final projection layer to vocabulary
            tokenizer: Optional tokenizer for token conversion
        """
        self.config = config
        self.delta_stack = delta_stack
        self.language_model_head = language_model_head
        self.tokenizer = tokenizer
        self.logger = logger
        
    def execute(
        self,
        initial_state: torch.Tensor,
        initial_semantic_states: List[List[torch.Tensor]],
        kappa_cache: KappaCache,
        max_length: int = 100,
        temperature: float = 1.0,
        top_k: Optional[int] = None,
        top_p: Optional[float] = None,
        do_sample: bool = True,
        eos_token_id: Optional[int] = None,
        pad_token_id: Optional[int] = None
    ) -> PhaseOutput:
        """
        Execute the Decoding Phase for autoregressive generation.
        
        Implements Algorithm from §4.2:
        1. Start with initial state from prefill final state
        2. For each new token t+1:
           a. Generate new thesis and antithesis
           b. Retrieve context from κ-Cache via attention
           c. Fuse new and cached antitheses
           d. Synthesize and predict next token
        3. Continue until max_length or EOS token
        
        Args:
            initial_state: Final state from prefill h_{plan}
            initial_semantic_states: Semantic attractor states from prefill
                                   List of length n_layers, each containing List[attractor_states]
            kappa_cache: Populated κ-Cache from prefill phase
            max_length: Maximum number of tokens to generate
            temperature: Sampling temperature for logits
            top_k: Top-k sampling parameter
            top_p: Top-p (nucleus) sampling parameter
            do_sample: Whether to sample or use greedy decoding
            eos_token_id: End-of-sequence token ID
            pad_token_id: Padding token ID
            
        Returns:
            PhaseOutput containing generated tokens and final states
        """
        if not kappa_cache.is_populated():
            raise ValueError("κ-Cache must be populated before decoding")
            
        batch_size = initial_state.shape[0]
        device = initial_state.device
        
        # Initialize generation state
        current_state = initial_state
        current_semantic_states = initial_semantic_states
        generated_tokens = []
        
        # Convert to token embeddings if needed (simplified - assumes token embedding layer exists)
        # In practice, this would use the model's embedding layer
        
        self.logger.info(f"Starting decoding for max_length={max_length}")
        
        for step in range(max_length):
            # Generate logits for next token
            try:
                # Convert semantic states to Optional format for DELTAStack
                optional_semantic_states = [states if states is not None else None for states in current_semantic_states]
                
                # Forward pass through DELTA stack with κ-Cache attention
                stack_output = self.delta_stack.forward(
                    h_in=current_state,
                    h_sem_in=optional_semantic_states,
                    kappa_cache=kappa_cache
                )
                
                # Project to vocabulary space
                logits = self.language_model_head(stack_output.h_out)  # (batch_size, vocab_size)
                
                # Apply temperature scaling
                if temperature != 1.0:
                    logits = logits / temperature
                
                # Sample next token
                next_token_id = self._sample_next_token(
                    logits=logits,
                    do_sample=do_sample,
                    top_k=top_k,
                    top_p=top_p
                )
                
                generated_tokens.append(next_token_id.cpu())
                
                # Check for early stopping
                if eos_token_id is not None and (next_token_id == eos_token_id).any():
                    self.logger.info(f"EOS token encountered at step {step}")
                    break
                
                # Prepare for next iteration
                # In a real implementation, this would convert token_id back to embedding
                # For now, we'll use the current state as a placeholder
                current_state = stack_output.h_out
                current_semantic_states = stack_output.h_sem_out
                
                # Apply numerical stability
                current_state = ensure_numerical_stability(current_state, f"decode_step_{step}")
                
                # Log progress for long sequences
                if step > 0 and step % 50 == 0:
                    self.logger.debug(f"Decoding progress: {step}/{max_length} tokens generated")
                    
            except Exception as e:
                self.logger.error(f"Error during decoding step {step}: {e}")
                break
        
        # Concatenate generated tokens
        if generated_tokens:
            generated_tensor = torch.cat(generated_tokens, dim=0)  # (max_length,) or (batch_size, max_length)
        else:
            generated_tensor = torch.empty((0,), dtype=torch.long, device=device)
        
        self.logger.info(f"Decoding completed. Generated {len(generated_tokens)} tokens")
        
        return PhaseOutput(
            final_state=current_state,
            generated_tokens=generated_tensor.tolist() if generated_tensor.numel() > 0 else [],
            semantic_states=current_semantic_states
        )
    
    def _sample_next_token(
        self,
        logits: torch.Tensor,
        do_sample: bool = True,
        top_k: Optional[int] = None,
        top_p: Optional[float] = None
    ) -> torch.Tensor:
        """Sample next token from logits using various strategies.
        
        Args:
            logits: Logits tensor of shape (batch_size, vocab_size)
            do_sample: Whether to sample or use greedy decoding
            top_k: Top-k sampling parameter
            top_p: Top-p (nucleus) sampling parameter
            
        Returns:
            Next token IDs of shape (batch_size,)
        """
        if not do_sample:
            # Greedy decoding
            return torch.argmax(logits, dim=-1)
        
        # Apply top-k filtering
        if top_k is not None and top_k > 0:
            top_k = min(top_k, logits.size(-1))
            # Get top-k logits and indices
            top_k_logits, top_k_indices = torch.topk(logits, top_k, dim=-1)
            # Set all other logits to -inf
            filtered_logits = torch.full_like(logits, float('-inf'))
            filtered_logits.scatter_(-1, top_k_indices, top_k_logits)
            logits = filtered_logits
        
        # Apply top-p (nucleus) filtering
        if top_p is not None and top_p < 1.0:
            sorted_logits, sorted_indices = torch.sort(logits, descending=True, dim=-1)
            cumulative_probs = torch.cumsum(F.softmax(sorted_logits, dim=-1), dim=-1)
            
            # Remove tokens with cumulative probability above the threshold
            sorted_indices_to_remove = cumulative_probs > top_p
            # Keep at least one token
            sorted_indices_to_remove[..., 1:] = sorted_indices_to_remove[..., :-1].clone()
            sorted_indices_to_remove[..., 0] = 0
            
            # Set filtered logits to -inf
            indices_to_remove = sorted_indices_to_remove.scatter(
                -1, sorted_indices, sorted_indices_to_remove
            )
            logits = logits.masked_fill(indices_to_remove, float('-inf'))
        
        # Sample from the filtered distribution
        probs = stable_softmax(logits, dim=-1)
        next_token = torch.multinomial(probs, num_samples=1).squeeze(-1)
        
        return next_token
    
    def generate_streaming(
        self,
        initial_state: torch.Tensor,
        initial_semantic_states: List[List[torch.Tensor]],
        kappa_cache: KappaCache,
        max_length: int = 100,
        temperature: float = 1.0,
        callback: Optional[Callable[[int, torch.Tensor], bool]] = None
    ) -> List[int]:
        """
        Generate tokens with streaming callback support.
        
        Args:
            initial_state: Final state from prefill
            initial_semantic_states: Semantic states from prefill
                                   List of length n_layers, each containing List[attractor_states]
            kappa_cache: Populated κ-Cache
            max_length: Maximum tokens to generate
            temperature: Sampling temperature
            callback: Optional callback function called after each token
                     Should return False to stop generation early
                     
        Returns:
            List of generated token IDs
        """
        if not kappa_cache.is_populated():
            raise ValueError("κ-Cache must be populated before streaming generation")
            
        current_state = initial_state
        current_semantic_states = initial_semantic_states
        generated_tokens = []
        
        for step in range(max_length):
            try:
                # Convert semantic states to Optional format for DELTAStack
                optional_semantic_states = [states if states is not None else None for states in current_semantic_states]
                
                # Forward pass through DELTA stack
                stack_output = self.delta_stack.forward(
                    h_in=current_state,
                    h_sem_in=optional_semantic_states,
                    kappa_cache=kappa_cache
                )
                
                # Generate next token
                logits = self.language_model_head(stack_output.h_out)
                if temperature != 1.0:
                    logits = logits / temperature
                    
                probs = stable_softmax(logits, dim=-1)
                next_token = torch.multinomial(probs, num_samples=1).squeeze(-1)
                
                generated_tokens.append(next_token.item() if next_token.numel() == 1 else next_token.cpu().tolist())
                
                # Call streaming callback if provided
                if callback is not None:
                    should_continue = callback(step, next_token)
                    if not should_continue:
                        break
                
                # Update state for next iteration
                current_state = stack_output.h_out
                current_semantic_states = self._convert_optional_states_to_list(stack_output.h_sem_out)
                
            except Exception as e:
                self.logger.error(f"Error in streaming generation at step {step}: {e}")
                break
        
        return generated_tokens
    
    def _convert_optional_states_to_list(
        self, 
        optional_states: List[Optional[List[torch.Tensor]]]
    ) -> List[List[torch.Tensor]]:
        """Convert from DELTAStack Optional format to standardized list format.
        
        Args:
            optional_states: States from DELTAStack with Optional wrapper
            
        Returns:
            Standardized list format, replacing None with empty lists
        """
        result = []
        for states in optional_states:
            if states is not None:
                result.append(states)
            else:
                # Initialize empty states for this layer if None
                result.append([])
        return result