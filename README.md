# DELTA: Dialectic Evaluative Layered Tension Attractor

A novel decoder architecture designed for robust logical reasoning through dialectic processes.

## Overview

DELTA introduces a two-phase dialectic decoder architecture with a κ-Cache mechanism for enhanced logical reasoning capabilities. Unlike traditional Transformers, DELTA performs structured dialectical arguments at every computational step, implementing a thesis-antithesis-synthesis process.

## Key Features

- **Dialectic Architecture**: Structured conflict between proposals (thesis) and constraints (antithesis)
- **κ-Cache System**: Efficient constraint vector caching for improved inference
- **Three-Phase Cycle**: Prefill, Decoding, and Mutation phases for adaptive reasoning
- **Dual-Objective Learning**: Separate optimization paths for proposal and constraint components
- **Attractor Systems**: Specialized structural and semantic constraint generators

## Architecture Components

### Core Components
- **DELTA Blocks**: Four-stage computational units with dialectic synthesis
- **Attractors**: Structural (stateless) and Semantic (recurrent) constraint generators
- **κ-Cache**: Layer-specific constraint vector storage and retrieval system
- **FFT Synthesis**: Frequency-domain convolution for thesis-antithesis resolution

### Operational Phases
1. **Prefill Phase**: Sequential prompt processing and κ-Cache population
2. **Decoding Phase**: Autoregressive generation with constraint verification
3. **Mutation Phase**: Essence extraction and horizon adaptation

## Installation

```bash
# Clone repository
git clone https://github.com/CursiveCrow/delta.git
cd delta

# Install package
pip install -e .

# Install with development dependencies
pip install -e ".[dev,experiment]"
```

## Quick Start

### Training
```bash
# Train a small model
make train-small

# Train base model
python scripts/train.py --config configs/base_model.yaml
```

### Inference
```bash
# Run inference
python scripts/inference.py --model-path checkpoints/model.pt --prompt "Your reasoning task here"
```

### Evaluation
```bash
# Evaluate reasoning capabilities
make eval-reasoning

# Evaluate constraint adherence
python scripts/evaluate.py --task constraints --model-path checkpoints/model.pt
```

## Project Structure

```
delta/
├── source/                 # Core DELTA implementation
│   ├── delta_block/       # DELTA block components
│   ├── attractors/        # Structural and semantic attractors
│   ├── cache/            # κ-Cache system
│   ├── phases/           # Three-phase operational cycle
│   ├── training/         # Dual-objective learning
│   ├── math/             # Mathematical operations
│   ├── models/           # High-level model interfaces
│   └── config/           # Configuration management
├── data/                  # Data pipeline and preprocessing
├── scripts/              # Training and evaluation scripts
├── tests/                # Test suite
└── docs/                 # Documentation
```

## Mathematical Framework

DELTA implements a formal dialectic process:

1. **Thesis Generation**: `V_T = W_T * h_in + b_T`
2. **Antithesis Generation**: Composite of structural and semantic forces
3. **Synthesis**: FFT-based convolution `V_S = real(iFFT(FFT(V_T) ⊙ FFT(κ)))`
4. **Refinement**: Residual connection and gated update

## Research Applications

DELTA is designed for tasks requiring:
- Deductive reasoning
- Logical constraint verification
- Multi-step problem solving
- Structured argument analysis
- Consistency maintenance

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make changes with tests
4. Run formatting: `make format`
5. Run tests: `make test`
6. Submit pull request

## Citation

```bibtex
@article{delta2025,
  title={DELTA: A Two-Phase Dialectic Decoder Architecture with κ-Cache for Robust Logical Reasoning},
  author={DELTA Research Team},
  journal={arXiv preprint},
  year={2025}
}
```

## License

MIT License - see LICENSE file for details.

## Contact

For questions and collaboration: <EMAIL>
