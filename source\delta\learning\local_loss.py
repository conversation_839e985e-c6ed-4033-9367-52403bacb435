"""Local tension loss computation functions for DELTA architecture.

This module provides helper functions to compute the local tension loss
inside the DELTA block as specified in Section 5.

The local tension loss is defined as:
L_local = 1/2 * ||V_T - detach(κ_effective)||^2

Where:
- V_T is the thesis vector (proposal)
- κ_effective is the effective antithesis (constraint)
- detach() prevents gradients from flowing through the antithesis
"""

import torch
from torch import Tensor
from typing import Optional


def compute_local_tension_loss(
    thesis: Tensor,
    effective_antithesis: Tensor,
    reduction: str = 'mean'
) -> Tensor:
    """Compute the local tension loss between thesis and effective antithesis.
    
    Implements the local tension loss from Spec §5:
    L_local = 1/2 * ||V_T - detach(κ_effective)||^2
    
    This loss encourages the thesis to align with the effective constraints
    while preventing gradients from flowing through the antithesis generation.
    
    Args:
        thesis: Thesis vector V_T of shape (batch_size, d_model)
        effective_antithesis: Effective antithesis κ_effective of shape (batch_size, d_model)
        reduction: How to reduce the loss ('mean', 'sum', or 'none')
        
    Returns:
        Scalar loss tensor if reduction != 'none', otherwise tensor of shape (batch_size,)
    """
    # Detach the antithesis to prevent gradient flow through constraint generation
    detached_antithesis = effective_antithesis.detach()
    
    # Compute squared L2 distance: ||V_T - detach(κ_effective)||^2
    squared_diff = torch.sum((thesis - detached_antithesis) ** 2, dim=-1)  # (batch_size,)
    
    # Apply 1/2 scaling factor
    loss = 0.5 * squared_diff
    
    # Apply reduction
    if reduction == 'mean':
        return loss.mean()
    elif reduction == 'sum':
        return loss.sum()
    elif reduction == 'none':
        return loss
    else:
        raise ValueError(f"Invalid reduction mode: {reduction}. Must be 'mean', 'sum', or 'none'")


def compute_batch_local_tension_loss(
    thesis_batch: Tensor,
    effective_antithesis_batch: Tensor,
    mask: Optional[Tensor] = None,
    reduction: str = 'mean'
) -> Tensor:
    """Compute local tension loss for a batch with optional masking.
    
    Args:
        thesis_batch: Batch of thesis vectors of shape (batch_size, seq_len, d_model)
        effective_antithesis_batch: Batch of effective antithesis vectors, same shape
        mask: Optional mask of shape (batch_size, seq_len) where True indicates valid positions
        reduction: How to reduce the loss ('mean', 'sum', or 'none')
        
    Returns:
        Scalar loss tensor if reduction != 'none', otherwise tensor of appropriate shape
    """
    # Flatten to (batch_size * seq_len, d_model) for computation
    batch_size, seq_len, d_model = thesis_batch.shape
    thesis_flat = thesis_batch.view(-1, d_model)
    antithesis_flat = effective_antithesis_batch.view(-1, d_model)
    
    # Compute per-token losses
    token_losses = compute_local_tension_loss(thesis_flat, antithesis_flat, reduction='none')
    token_losses = token_losses.view(batch_size, seq_len)  # (batch_size, seq_len)
    
    # Apply mask if provided
    if mask is not None:
        token_losses = token_losses * mask.float()
        
        if reduction == 'mean':
            return token_losses.sum() / mask.sum().clamp(min=1)
        elif reduction == 'sum':
            return token_losses.sum()
        elif reduction == 'none':
            return token_losses
    else:
        if reduction == 'mean':
            return token_losses.mean()
        elif reduction == 'sum':
            return token_losses.sum()
        elif reduction == 'none':
            return token_losses
    
    raise ValueError(f"Invalid reduction mode: {reduction}")


def local_tension_loss_with_regularization(
    thesis: Tensor,
    effective_antithesis: Tensor,
    thesis_params: list,
    l2_weight: float = 1e-5,
    reduction: str = 'mean'
) -> Tensor:
    """Compute local tension loss with L2 regularization on thesis parameters.
    
    Total loss = L_local + λ * ||θ_thesis||^2
    
    Args:
        thesis: Thesis vector V_T
        effective_antithesis: Effective antithesis κ_effective  
        thesis_params: List of thesis parameters for regularization
        l2_weight: L2 regularization weight
        reduction: How to reduce the loss
        
    Returns:
        Combined loss tensor
    """
    # Main local tension loss
    main_loss = compute_local_tension_loss(thesis, effective_antithesis, reduction)
    
    # L2 regularization on thesis parameters
    l2_loss = 0.0
    for param in thesis_params:
        l2_loss += torch.sum(param ** 2)
    
    return main_loss + l2_weight * l2_loss