"""Reusable neural network components for DELTA architecture.

This module contains parameterized neural network building blocks that can be
used across different parts of the DELTA architecture. These are distinct from
pure mathematical functions (in math/) and DELTA-specific implementations (in core/).

Components are:
- Stateful (have parameters and weights)
- Reusable across different parts of the architecture
- Standard neural network building blocks
- Not specific to DELTA's dialectic process
"""

# Feed-forward networks
from .ffn import (
    FeedForwardNetwork,
    GLUFeedForwardNetwork,
    create_ffn
)

# Attention mechanisms
from .attention import (
    KappaCacheAttention
)

# Operator components
from .operator import (
    LowRankOperator
)

# Normalization components
from .normalization import (
    LayerNorm,
    RMSNorm,
    GroupNorm,
    AdaptiveLayerNorm,
    PowerNorm,
    ScaleNorm,
    CrossNorm,
    batch_norm_1d,
    instance_norm,
    spectral_norm
)

__all__ = [
    # Feed-forward networks
    'FeedForwardNetwork',
    'GLUFeedForwardNetwork',
    'create_ffn',

    # Attention mechanisms
    'KappaCacheAttention',

    # Operator components
    'LowRankOperator',

    # Normalization components
    'LayerNorm',
    'RMSNorm',
    'GroupNorm',
    'AdaptiveLayerNorm',
    'PowerNorm',
    'ScaleNorm',
    'CrossNorm',
    'batch_norm_1d',
    'instance_norm',
    'spectral_norm'
]
