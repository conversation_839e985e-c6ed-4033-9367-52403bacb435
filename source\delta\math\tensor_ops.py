"""General tensor operations - utility functions for tensor manipulation.

This module provides essential tensor operations used throughout the DELTA
architecture, including reshaping, concatenation, and manipulation utilities.
"""

import torch
from torch import Tensor
import torch.nn.functional as F
from typing import List, Optional, Tuple, Union

from ..utils.constants import EPSILON


def safe_concat(
    tensors: List[Tensor],
    dim: int = -1,
    handle_empty: bool = True
) -> Tensor:
    """Safely concatenate tensors along a dimension.
    
    Args:
        tensors: List of tensors to concatenate
        dim: Dimension to concatenate along
        handle_empty: Whether to handle empty tensor lists
        
    Returns:
        Concatenated tensor
    """
    if len(tensors) == 0:
        raise ValueError("Cannot concatenate empty list of tensors")
    
    # Filter out None values
    tensors = [t for t in tensors if t is not None]
    
    if len(tensors) == 0:
        raise ValueError("Cannot concatenate empty list of tensors after filtering None values")
    elif len(tensors) == 1:
        return tensors[0]
    else:
        return torch.cat(tensors, dim=dim)


def split_tensor(
    tensor: Tensor,
    split_sizes: Union[int, List[int]],
    dim: int = -1
) -> List[Tensor]:
    """Split tensor into chunks.
    
    Args:
        tensor: Input tensor
        split_sizes: Size of each chunk or list of sizes
        dim: Dimension to split along
        
    Returns:
        List of tensor chunks
    """
    if isinstance(split_sizes, int):
        # Equal splits
        return torch.chunk(tensor, split_sizes, dim=dim)
    else:
        # Custom splits
        return torch.split(tensor, split_sizes, dim=dim)


def reshape_for_broadcast(
    tensor: Tensor,
    target_shape: Tuple[int, ...],
    dim_mapping: Optional[List[int]] = None
) -> Tensor:
    """Reshape tensor for broadcasting with target shape.
    
    Args:
        tensor: Input tensor
        target_shape: Target shape to broadcast to
        dim_mapping: Mapping of tensor dims to target dims
        
    Returns:
        Reshaped tensor ready for broadcasting
    """
    if dim_mapping is None:
        # Default: add dimensions to make tensor broadcastable with target
        # Strategy: insert 1s in the middle while preserving alignment of first and last dims
        tensor_shape = list(tensor.shape)
        target_dims = len(target_shape)
        tensor_dims = len(tensor_shape)
        
        if tensor_dims >= target_dims:
            # Tensor already has same or more dimensions
            return tensor
        
        # Insert 1s to match the target number of dimensions
        # Insert before the last dimension to preserve the pattern
        new_shape = tensor_shape[:]
        dims_to_add = target_dims - tensor_dims
        
        # Insert 1s at appropriate positions
        for i in range(dims_to_add):
            # Insert at position that makes broadcasting work
            # For (4, 64) -> (4, 8, 64), insert at position 1 to get (4, 1, 64)
            insert_pos = len(new_shape) - 1  # Before last dimension
            new_shape.insert(insert_pos, 1)
    else:
        # Use explicit mapping
        new_shape = [1] * len(target_shape)
        for i, dim_idx in enumerate(dim_mapping):
            if dim_idx < len(target_shape) and i < len(tensor.shape):
                new_shape[dim_idx] = tensor.shape[i]
    
    return tensor.reshape(new_shape)


def masked_mean(
    tensor: Tensor,
    mask: Optional[Tensor] = None,
    dim: Optional[Union[int, Tuple[int, ...]]] = None,
    keepdim: bool = False
) -> Tensor:
    """Compute mean with optional masking.
    
    Per DeltaDecoder.md Section 4.1, supports variable-length sequences with proper broadcasting.
    
    Args:
        tensor: Input tensor
        mask: Boolean mask (True for valid positions)
        dim: Dimension(s) to reduce
        keepdim: Whether to keep reduced dimensions
        
    Returns:
        Masked mean
    """
    if mask is None:
        return tensor.mean(dim=dim, keepdim=keepdim)
    
    # Ensure mask can broadcast with tensor
    while mask.dim() < tensor.dim():
        mask = mask.unsqueeze(-1)
    
    # Apply mask
    masked_tensor = tensor * mask.float()
    
    # Count valid positions
    valid_count = mask.float().sum(dim=dim, keepdim=keepdim)
    valid_count = torch.clamp(valid_count, min=EPSILON)
    
    # Compute mean over valid positions
    return masked_tensor.sum(dim=dim, keepdim=keepdim) / valid_count


def masked_sum(
    tensor: Tensor,
    mask: Optional[Tensor] = None,
    dim: Optional[Union[int, Tuple[int, ...]]] = None,
    keepdim: bool = False
) -> Tensor:
    """Compute sum with optional masking.
    
    Args:
        tensor: Input tensor
        mask: Boolean mask (True for valid positions)
        dim: Dimension(s) to reduce
        keepdim: Whether to keep reduced dimensions
        
    Returns:
        Masked sum
    """
    if mask is None:
        return tensor.sum(dim=dim, keepdim=keepdim)
    
    # Ensure mask can broadcast with tensor
    while mask.dim() < tensor.dim():
        mask = mask.unsqueeze(-1)
    
    # Apply mask and sum
    masked_tensor = tensor * mask.float()
    return masked_tensor.sum(dim=dim, keepdim=keepdim)


def normalize_tensor(
    tensor: Tensor,
    p: float = 2.0,
    dim: int = -1,
    eps: float = EPSILON
) -> Tensor:
    """Normalize tensor along dimension.
    
    Args:
        tensor: Input tensor
        p: Norm type (1, 2, inf)
        dim: Dimension to normalize along
        eps: Epsilon for numerical stability
        
    Returns:
        Normalized tensor
    """
    return F.normalize(tensor, p=p, dim=dim, eps=eps)


def tensor_statistics(
    tensor: Tensor,
    dim: Optional[Union[int, Tuple[int, ...]]] = None
) -> dict:
    """Compute comprehensive statistics of a tensor.
    
    Args:
        tensor: Input tensor
        dim: Dimension(s) to compute statistics along
        
    Returns:
        Dictionary with statistics
    """
    stats = {
        'mean': tensor.mean(dim=dim),
        'var': tensor.var(dim=dim),  # Required for stability analysis per DeltaDecoder.md
        'std': tensor.std(dim=dim),
        'min': tensor.min() if dim is None else tensor.min(dim=dim)[0],
        'max': tensor.max() if dim is None else tensor.max(dim=dim)[0],
        'norm_l1': tensor.abs().sum(dim=dim),
        'norm_l2': tensor.norm(p=2, dim=dim),
        'norm_inf': tensor.abs().max() if dim is None else tensor.abs().max(dim=dim)[0]
    }
    return stats


def create_causal_mask(
    seq_len: int,
    device: torch.device,
    additive: bool = False,
    dtype: Optional[torch.dtype] = None
) -> Tensor:
    """Create a causal mask for autoregressive processing.

    Args:
        seq_len: Sequence length.
        device: Device to create the mask on.
        additive: If True, create an additive mask with -inf. Otherwise, a boolean mask.
        dtype: Data type of the mask. Defaults to torch.bool for multiplicative and torch.float32 for additive.

    Returns:
        Causal mask of shape (seq_len, seq_len).
    """
    if additive:
        mask_dtype = dtype if dtype is not None else torch.float32
        mask = torch.triu(
            torch.full((seq_len, seq_len), float('-inf'), device=device, dtype=mask_dtype),
            diagonal=1
        )
    else:
        mask_dtype = dtype if dtype is not None else torch.bool
        mask = torch.tril(torch.ones(seq_len, seq_len, device=device, dtype=mask_dtype))
    return mask


def apply_rotary_embeddings(
    tensor: Tensor,
    positions: Tensor,
    dim: int,
    base: float = 10000.0
) -> Tensor:
    """Apply rotary position embeddings.
    
    Args:
        tensor: Input tensor of shape (..., seq_len, dim)
        positions: Position indices
        dim: Embedding dimension
        base: Base for frequency computation
        
    Returns:
        Tensor with rotary embeddings applied
    """
    device = tensor.device
    dtype = tensor.dtype
    seq_len = tensor.shape[-2]
    
    # Compute frequencies
    inv_freq = 1.0 / (base ** (torch.arange(0, dim, 2, device=device, dtype=dtype) / dim))
    
    # Compute rotary embeddings
    sinusoid_inp = positions.unsqueeze(-1) * inv_freq.unsqueeze(0)
    sin = sinusoid_inp.sin()
    cos = sinusoid_inp.cos()
    
    # Apply rotation
    x1, x2 = tensor[..., ::2], tensor[..., 1::2]
    tensor_rot = torch.stack(
        [x1 * cos - x2 * sin, x1 * sin + x2 * cos],
        dim=-1
    ).flatten(-2)
    
    return tensor_rot


def circular_shift(
    tensor: Tensor,
    shifts: Union[int, Tuple[int, ...]],
    dims: Optional[Union[int, Tuple[int, ...]]] = None
) -> Tensor:
    """Perform circular shift on tensor.
    
    Args:
        tensor: Input tensor
        shifts: Number of positions to shift
        dims: Dimensions to shift along
        
    Returns:
        Shifted tensor
    """
    return torch.roll(tensor, shifts=shifts, dims=dims)


def repeat_interleave_batch(
    tensor: Tensor,
    repeats: int,
    batch_dim: int = 0
) -> Tensor:
    """Repeat tensor along batch dimension with interleaving.
    
    Args:
        tensor: Input tensor
        repeats: Number of repetitions
        batch_dim: Batch dimension
        
    Returns:
        Repeated tensor
    """
    return tensor.repeat_interleave(repeats, dim=batch_dim)


def gather_from_indices(
    tensor: Tensor,
    indices: Tensor,
    dim: int = -1
) -> Tensor:
    """Gather values from tensor using indices.
    
    Args:
        tensor: Input tensor
        indices: Indices to gather 
        dim: Dimension to gather along
        
    Returns:
        Gathered tensor
    """
    # For torch.gather, indices must have the same shape as the desired output
    # Normalize dim to positive
    if dim < 0:
        dim = tensor.dim() + dim
    
    # Different cases for indices shapes:
    # Case 1: indices has same dimensionality as tensor
    if indices.dim() == tensor.dim():
        # Direct use - indices should already be properly shaped
        return torch.gather(tensor, dim=dim, index=indices)
    
    # Case 2: indices has fewer dimensions - need to expand
    if indices.dim() < tensor.dim():
        indices_expanded = indices
        
        # Add dimensions in the right places
        # We need to add dimensions everywhere except the gather dimension
        target_ndim = tensor.dim()
        current_ndim = indices.dim()
        
        # Add dimensions at the end until we have the right number
        while indices_expanded.dim() < target_ndim:
            indices_expanded = indices_expanded.unsqueeze(-1)
        
        # Build target shape: same as tensor except at gather dimension
        target_shape = list(tensor.shape)
        
        # The gather dimension size comes from the original indices shape
        if dim < indices.dim():
            target_shape[dim] = indices.shape[dim]
        else:
            # If gather dim is beyond original indices dims, use last dim of indices
            target_shape[dim] = indices.shape[-1]
        
        # Expand indices to target shape
        indices_expanded = indices_expanded.expand(target_shape)
        
        return torch.gather(tensor, dim=dim, index=indices_expanded)
    
    # Case 3: indices has more dimensions than tensor
    else:
        raise ValueError(f"Indices tensor has {indices.dim()} dimensions but input tensor has {tensor.dim()} dimensions")


def scatter_to_indices(
    tensor: Tensor,
    indices: Tensor,
    values: Tensor,
    dim: int = -1,
    reduce: str = "replace"
) -> Tensor:
    """Scatter values to tensor at indices.
    
    Args:
        tensor: Target tensor
        indices: Indices to scatter to
        values: Values to scatter
        dim: Dimension to scatter along
        reduce: Reduction method ("mean", "sum", "replace")
        
    Returns:
        Tensor with scattered values
    """
    # For torch.scatter, indices must have the same shape as values
    # Normalize dim to positive
    if dim < 0:
        dim = tensor.dim() + dim
    
    # Expand indices to match values shape
    indices_expanded = indices
    
    # Add dimensions to match values dimensions
    while indices_expanded.dim() < values.dim():
        indices_expanded = indices_expanded.unsqueeze(-1)
    
    # Expand to the values shape
    indices_expanded = indices_expanded.expand_as(values)
    
    if reduce == "replace":
        return tensor.scatter(dim, indices_expanded, values)
    else:
        return tensor.scatter_reduce(dim, indices_expanded, values, reduce=reduce)


def ensure_contiguous(tensor: Tensor) -> Tensor:
    """Ensure tensor is contiguous in memory.
    
    Args:
        tensor: Input tensor
        
    Returns:
        Contiguous tensor
    """
    if not tensor.is_contiguous():
        return tensor.contiguous()
    return tensor


def compute_pairwise_distances(
    x: Tensor,
    y: Optional[Tensor] = None,
    p: float = 2.0
) -> Tensor:
    """Compute pairwise distances between tensors.
    
    Args:
        x: First tensor of shape (n, dim) or (batch_size, n, dim)
        y: Second tensor of shape (m, dim) or (batch_size, m, dim)
        p: Norm type
        
    Returns:
        Distance matrix of shape (n, m) or (batch_size, n, m)
    """
    if y is None:
        y = x
    
    # Check if we're computing self-distances
    is_self_distance = y is x
    
    # Handle 2D tensors by adding batch dimension
    if x.dim() == 2:
        x = x.unsqueeze(0)
    if y.dim() == 2:
        y = y.unsqueeze(0)
        
    squeeze_result = x.size(0) == 1 and y.size(0) == 1
    
    if p == 2:
        # Efficient L2 distance computation
        xx = (x * x).sum(dim=-1, keepdim=True)
        yy = (y * y).sum(dim=-1, keepdim=True).transpose(-2, -1)
        xy = torch.bmm(x, y.transpose(-2, -1))
        distances = torch.sqrt(torch.clamp(xx + yy - 2 * xy, min=0))
        
        # For self-distances, ensure diagonal is exactly zero
        if is_self_distance:
            batch_size, n, _ = distances.shape
            for b in range(batch_size):
                distances[b].fill_diagonal_(0.0)
    else:
        # General Lp distance
        x_expanded = x.unsqueeze(-2)  # (batch, n, 1, dim)
        y_expanded = y.unsqueeze(-3)  # (batch, 1, m, dim)
        distances = torch.norm(x_expanded - y_expanded, p=p, dim=-1)
        
        # For self-distances, ensure diagonal is exactly zero
        if is_self_distance:
            batch_size, n, _ = distances.shape
            for b in range(batch_size):
                distances[b].fill_diagonal_(0.0)
    
    # Remove batch dimension if inputs were 2D
    if squeeze_result:
        distances = distances.squeeze(0)
    
    return distances


def apply_temperature_scaling(
    logits: Tensor,
    temperature: float = 1.0,
    dim: int = -1
) -> Tensor:
    """Apply temperature scaling to logits.
    
    Per DeltaDecoder.md Section 4.2, temperature scaling should scale logits,
    not return probabilities.
    
    Args:
        logits: Input logits
        temperature: Temperature value
        dim: Dimension for scaling
        
    Returns:
        Temperature-scaled logits
    """
    if temperature == 0:
        # For zero temperature, perform argmax and return one-hot logits
        # to avoid numerical instability from large scaling factors.
        argmax_indices = torch.argmax(logits, dim=dim, keepdim=True)
        one_hot_logits = torch.zeros_like(logits)
        one_hot_logits.scatter_(dim, argmax_indices, 1.0)
        # Scale by a large value to maintain the logit nature
        return one_hot_logits * 1e6
    else:
        return logits / temperature


def batch_diagonal(
    tensor: Tensor,
    diagonal: int = 0
) -> Tensor:
    """Extract diagonal from batch of matrices.
    
    Args:
        tensor: Input tensor of shape (batch_size, n, m)
        diagonal: Diagonal offset
        
    Returns:
        Diagonal elements
    """
    return torch.diagonal(tensor, offset=diagonal, dim1=-2, dim2=-1)
