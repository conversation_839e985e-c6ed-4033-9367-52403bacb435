"""Model constants and defaults for DELTA architecture.

This module contains fixed values and default parameters used throughout
the DELTA implementation.
"""

import torch

# Model architecture constants
DEFAULT_D_MODEL = 512
DEFAULT_N_LAYERS = 12
DEFAULT_N_ATTRACTORS = 8
DEFAULT_N_HEADS = 8
DEFAULT_DROPOUT = 0.1
DEFAULT_MAX_SEQ_LENGTH = 2048
DEFAULT_VOCAB_SIZE = 50257

# Attractor-specific constants
DEFAULT_SEMANTIC_HIDDEN_SIZE = 256
DEFAULT_STRUCTURAL_PROJ_SIZE = 256

# Learning rate defaults
DEFAULT_THESIS_LR = 1e-3
DEFAULT_ANTITHESIS_LR = 1e-3

# Mutation phase constants
DEFAULT_MUTATION_RATE = 0.1
ESSENCE_EXTRACTION_METHODS = ["mean", "max", "learned"]
DEFAULT_ESSENCE_METHOD = "mean"

# Numerical stability constants
EPSILON = 1e-8
FFT_NORM_MODE = "ortho"  # Orthonormal FFT for stability

# Temperature and sampling
DEFAULT_TEMPERATURE = 1.0
MIN_TEMPERATURE = 0.1
MAX_TEMPERATURE = 2.0

# Special tokens
PAD_TOKEN_ID = 0
BOS_TOKEN_ID = 1
EOS_TOKEN_ID = 2
UNK_TOKEN_ID = 3

# Cache constants
CACHE_DTYPE = torch.float32
CACHE_DEVICE = None  # Will be set based on model device

# Gradient clipping
MAX_GRAD_NORM = 1.0

# Loss weights
GLOBAL_LOSS_WEIGHT = 1.0
LOCAL_LOSS_WEIGHT = 0.5

# Initialization ranges
INIT_RANGE = 0.02
ATTRACTOR_INIT_RANGE = 0.01

# Gating temperature for softmax
GATING_TEMPERATURE = 1.0

# Success detection thresholds for mutation
SUCCESS_THRESHOLD = 0.8
MIN_GENERATION_LENGTH = 10

# Attention constants
ATTENTION_DROPOUT = 0.1
ATTENTION_SCALE = None  # Will be sqrt(d_k) if None

# FFN expansion factor
FFN_EXPANSION_FACTOR = 4

# Layer normalization
LAYER_NORM_EPS = 1e-5

# Update gate bias initialization
UPDATE_GATE_BIAS_INIT = -2.0  # Bias towards preserving input initially

# Frequency domain operations
FFT_WINDOW_TYPE = None  # No windowing by default
CONVOLUTION_MODE = "circular"  # Circular convolution via FFT

# Training constants
WARMUP_STEPS = 4000
GRADIENT_ACCUMULATION_STEPS = 1
EVAL_STEPS = 500
SAVE_STEPS = 1000
LOGGING_STEPS = 100

# Memory optimization
GRADIENT_CHECKPOINTING = False
MIXED_PRECISION = False

# Validation constants
MAX_POSITION_EMBEDDINGS = 2048
HIDDEN_ACT = "gelu"

# Component names for gradient routing
THESIS_COMPONENTS = ["thesis_proj", "W_T", "b_T"]
ANTITHESIS_COMPONENTS = [
    "structural_attractors",
    "semantic_attractors", 
    "gating_struct",
    "gating_sem",
    "W_kappa",
    "b_kappa"
]
CONTROL_COMPONENTS = ["update_gate", "W_z", "b_z", "layer_norm", "ffn"]

# Debugging and logging
DEBUG_MODE = False
LOG_ATTENTION_WEIGHTS = False
LOG_GATE_WEIGHTS = False
LOG_SYNTHESIS_STATS = False

# Device placement
FORCE_CPU = False
DEVICE_MAP = "auto"  # For multi-GPU

# Experimental features
ENABLE_DYNAMIC_ATTRACTORS = False
ENABLE_ATTRACTOR_POOL = False
ATTRACTOR_POOL_SIZE = 32
NOVELTY_THRESHOLD = 0.9
