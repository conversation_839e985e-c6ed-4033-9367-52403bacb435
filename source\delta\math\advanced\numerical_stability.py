"""Numerical stability utilities for DELTA architecture.

This module provides utilities to ensure numerical stability throughout
the DELTA model, supporting Theorem 1 (State Stability) which proves
that state vectors remain bounded for all timesteps.
"""

import torch
from torch import Tensor
import torch.nn.functional as F
from typing import Optional, Union, Tuple
import math
import logging

from ...utils.constants import EPSILON, LAYER_NORM_EPS

logger = logging.getLogger(__name__)


def safe_log(x: Tensor, eps: float = EPSILON) -> Tensor:
    """Compute logarithm with numerical stability.
    
    Args:
        x: Input tensor
        eps: Small constant for stability
        
    Returns:
        log(x + eps)
    """
    return torch.log(x + eps)


def safe_sqrt(x: Tensor, eps: float = EPSILON) -> Tensor:
    """Compute square root with numerical stability.

    For negative values, clamps to 0. For zero and positive values,
    computes sqrt normally.

    Args:
        x: Input tensor
        eps: Small constant for stability (unused, kept for compatibility)

    Returns:
        sqrt(max(x, 0))
    """
    return torch.sqrt(torch.clamp(x, min=0.0))


def safe_divide(
    numerator: Tensor,
    denominator: Tensor,
    eps: float = EPSILON
) -> Tensor:
    """Safe division avoiding divide-by-zero.
    
    Args:
        numerator: Numerator tensor
        denominator: Denominator tensor
        eps: Small constant for stability
        
    Returns:
        numerator / (denominator + eps)
    """
    return numerator / (denominator + eps)


def safe_norm(
    x: Tensor,
    p: float = 2.0,
    dim: Optional[Union[int, Tuple[int, ...]]] = None,
    eps: float = EPSILON,
    keepdim: bool = False
) -> Tensor:
    """Compute norm with numerical stability.
    
    Args:
        x: Input tensor
        p: Norm order
        dim: Dimension(s) to compute norm over
        eps: Small constant for stability
        keepdim: Keep reduced dimensions
        
    Returns:
        Stable norm computation
    """
    if p == 2:
        # Special case for L2 norm
        return safe_sqrt((x ** 2).sum(dim=dim, keepdim=keepdim), eps=eps)
    else:
        return torch.norm(x, p=p, dim=dim, keepdim=keepdim) + eps


def clip_by_value(
    x: Tensor,
    min_value: float = -1e6,
    max_value: float = 1e6
) -> Tensor:
    """Clip tensor values to prevent overflow/underflow.
    
    Args:
        x: Input tensor
        min_value: Minimum allowed value
        max_value: Maximum allowed value
        
    Returns:
        Clipped tensor
    """
    return torch.clamp(x, min=min_value, max=max_value)


def clip_by_norm(
    x: Tensor,
    max_norm: float,
    norm_type: float = 2.0,
    dim: Optional[Union[int, Tuple[int, ...]]] = None
) -> Tensor:
    """Clip tensor by norm to ensure bounded values.
    
    This supports Theorem 1 by ensuring outputs remain bounded.
    
    Args:
        x: Input tensor
        max_norm: Maximum allowed norm
        norm_type: Type of norm
        dim: Dimension(s) to compute norm over
        
    Returns:
        Norm-clipped tensor
    """
    norm = torch.norm(x, p=norm_type, dim=dim, keepdim=True)
    scale = torch.clamp(max_norm / (norm + EPSILON), max=1.0)
    return x * scale


def stable_softmax(
    x: Tensor,
    dim: int = -1,
    temperature: float = 1.0
) -> Tensor:
    """Numerically stable softmax computation.
    
    Args:
        x: Input logits
        dim: Dimension to apply softmax
        temperature: Temperature scaling
        
    Returns:
        Stable softmax probabilities
    """
    # Subtract max for numerical stability
    x_max = x.max(dim=dim, keepdim=True)[0]
    x_stable = (x - x_max) / temperature
    return F.softmax(x_stable, dim=dim)


def stable_log_softmax(
    x: Tensor,
    dim: int = -1,
    temperature: float = 1.0
) -> Tensor:
    """Numerically stable log-softmax computation.
    
    Args:
        x: Input logits
        dim: Dimension to apply log-softmax
        temperature: Temperature scaling
        
    Returns:
        Stable log-softmax values
    """
    x_max = x.max(dim=dim, keepdim=True)[0]
    x_stable = (x - x_max) / temperature
    return F.log_softmax(x_stable, dim=dim)


def prevent_gradient_explosion(
    param: torch.nn.Parameter,
    max_norm: float = 1.0
) -> None:
    """Prevent gradient explosion by clipping gradients in-place.

    Args:
        param: Parameter with gradients to clip
        max_norm: Maximum gradient norm threshold
    """
    if param.grad is not None:
        torch.nn.utils.clip_grad_norm_(param, max_norm)


def stable_layer_norm(
    x: Tensor,
    normalized_shape: Tuple[int, ...],
    weight: Optional[Tensor] = None,
    bias: Optional[Tensor] = None,
    eps: float = LAYER_NORM_EPS
) -> Tensor:
    """Numerically stable layer normalization.
    
    Ensures bounded outputs as required by Theorem 1.
    
    Args:
        x: Input tensor
        normalized_shape: Shape to normalize over
        weight: Scale parameter
        bias: Shift parameter
        eps: Epsilon for stability
        
    Returns:
        Stably normalized tensor
    """
    # Compute mean and variance with high precision
    mean = x.mean(dim=-1, keepdim=True)
    var = ((x - mean) ** 2).mean(dim=-1, keepdim=True)
    
    # Normalize with stable sqrt
    x_norm = (x - mean) / safe_sqrt(var, eps=eps)
    
    # Apply affine transform if provided
    if weight is not None:
        x_norm = x_norm * weight
    if bias is not None:
        x_norm = x_norm + bias
    
    # Ensure output is bounded
    return clip_by_value(x_norm)


def stable_gelu(x: Tensor) -> Tensor:
    """Numerically stable GELU activation.
    
    Args:
        x: Input tensor
        
    Returns:
        GELU activation with bounded output
    """
    # Clip input to prevent numerical issues
    x_clipped = clip_by_value(x, -10.0, 10.0)
    return F.gelu(x_clipped)


def stable_sigmoid(x: Tensor) -> Tensor:
    """Numerically stable sigmoid.
    
    Args:
        x: Input tensor
        
    Returns:
        Stable sigmoid output
    """
    # Clip to prevent overflow in exp
    x_clipped = clip_by_value(x, -50.0, 50.0)
    return torch.sigmoid(x_clipped)


def stable_tanh(x: Tensor) -> Tensor:
    """Numerically stable tanh.
    
    Args:
        x: Input tensor
        
    Returns:
        Stable tanh output
    """
    # Tanh is already bounded [-1, 1] but clip input for stability
    x_clipped = clip_by_value(x, -20.0, 20.0)
    return torch.tanh(x_clipped)


class NumericalStabilizer(torch.nn.Module):
    """Module to ensure numerical stability of tensors.
    
    This can be inserted into the model to ensure Theorem 1 holds.
    """
    
    def __init__(
        self,
        max_norm: float = 100.0,
        eps: float = EPSILON,
        clip_value: float = 1e4
    ):
        super().__init__()
        self.max_norm = max_norm
        self.eps = eps
        self.clip_value = clip_value
    
    def forward(self, x: Tensor) -> Tensor:
        """Stabilize tensor values.
        
        Args:
            x: Input tensor
            
        Returns:
            Stabilized tensor
        """
        # Clip by value
        x = clip_by_value(x, -self.clip_value, self.clip_value)
        
        # Clip by norm
        x = clip_by_norm(x, self.max_norm)
        
        # Replace NaN/Inf with zeros
        x = torch.where(torch.isfinite(x), x, torch.zeros_like(x))
        
        return x

    def stabilize_tensor(self, x: Tensor) -> Tensor:
        """Stabilize tensor values (alias for forward).

        Args:
            x: Input tensor

        Returns:
            Stabilized tensor
        """
        return self.forward(x)


def check_numerical_stability(
    tensor: Tensor,
    name: str = "tensor",
    raise_on_error: bool = True
) -> bool:
    """Check if tensor contains NaN or Inf values.
    
    Args:
        tensor: Tensor to check
        name: Name for error messages
        raise_on_error: Whether to raise exception on error
        
    Returns:
        True if tensor is stable, False otherwise
    """
    has_nan = torch.isnan(tensor).any().item()
    has_inf = torch.isinf(tensor).any().item()
    
    if has_nan or has_inf:
        msg = f"{name} contains "
        if has_nan:
            msg += "NaN values"
        if has_inf:
            msg += " and " if has_nan else ""
            msg += "Inf values"
        
        if raise_on_error:
            raise ValueError(msg)
        else:
            print(f"Warning: {msg}")
            return False
    
    return True


def compute_bounded_attention_scores(
    query: Tensor,
    key: Tensor,
    scale: Optional[float] = None,
    max_value: float = 50.0
) -> Tensor:
    """Compute attention scores with bounds to ensure stability.
    
    Args:
        query: Query tensor
        key: Key tensor
        scale: Attention scale
        max_value: Maximum score value
        
    Returns:
        Bounded attention scores
    """
    d_k = query.size(-1)
    if scale is None:
        scale = 1.0 / math.sqrt(d_k)
    
    # Compute scores
    scores = torch.matmul(query, key.transpose(-2, -1)) * scale
    
    # Clip scores to prevent overflow in softmax
    scores = clip_by_value(scores, -max_value, max_value)
    
    return scores


def stabilize_gradients(module: torch.nn.Module) -> None:
    """Register hooks to stabilize gradients in a module.
    
    Args:
        module: Module to stabilize
    """
    def clip_grad_hook(grad: Tensor) -> Tensor:
        # Clip gradient values
        grad = clip_by_value(grad, -1e3, 1e3)
        
        # Replace NaN/Inf with zeros
        grad = torch.where(torch.isfinite(grad), grad, torch.zeros_like(grad))
        
        return grad
    
    # Register hooks on all parameters
    for param in module.parameters():
        if param.requires_grad:
            param.register_hook(clip_grad_hook)


def ensure_numerical_stability(x: Tensor, operation_name: str = "unknown") -> Tensor:
    """Ensure numerical stability for potentially unstable operations.
    
    This function wraps operations that may overflow or produce NaN/Inf values,
    such as FFT-based synthesis in the DELTA architecture.
    
    Args:
        x: Input tensor to stabilize
        operation_name: Name of the operation for logging purposes
        
    Returns:
        Numerically stable version of the input tensor
    """
    # Check for and handle problematic values
    if not torch.isfinite(x).all():
        logger.warning(f"Non-finite values detected in {operation_name}, applying stabilization")
        x = torch.where(torch.isfinite(x), x, torch.zeros_like(x))
    
    # Clip extreme values to prevent overflow
    x = clip_by_value(x, min_val=-1e6, max_val=1e6)
    
    return x


def stable_cross_entropy(
    logits: Tensor,
    targets: Tensor,
    reduction: str = 'mean'
) -> Tensor:
    """Numerically stable cross-entropy loss.
    
    Args:
        logits: Model predictions
        targets: Target labels
        reduction: Reduction method
        
    Returns:
        Stable cross-entropy loss
    """
    # Use log_softmax for stability
    log_probs = stable_log_softmax(logits, dim=-1)
    
    # Compute cross-entropy
    if targets.dim() == logits.dim():
        # Soft targets
        loss = -(targets * log_probs).sum(dim=-1)
    else:
        # Hard targets
        loss = F.nll_loss(log_probs, targets, reduction='none')
    
    if reduction == 'mean':
        return loss.mean()
    elif reduction == 'sum':
        return loss.sum()
    else:
        return loss