"""Mix-in classes for component categorization in dual-objective learning.

These mix-ins help the DualObjectiveLearner categorize parameters correctly
without relying on fragile regex-based name matching.
"""

from abc import ABC
from typing import List
import torch.nn as nn


class ThesisComponent(ABC):
    """Mix-in for components that should be updated by local tension loss.
    
    Components implementing this mix-in have their parameters updated
    exclusively by the local tension loss in dual-objective learning.
    """
    
    def get_thesis_parameters(self) -> List[nn.Parameter]:
        """Return parameters that should be updated by local loss."""
        return list(self.parameters()) if isinstance(self, nn.Module) else []


class AntithesisComponent(ABC):
    """Mix-in for components that should be updated by global task loss.
    
    Components implementing this mix-in have their parameters updated
    exclusively by the global task loss in dual-objective learning.
    """
    
    def get_antithesis_parameters(self) -> List[nn.Parameter]:
        """Return parameters that should be updated by global loss."""
        return list(self.parameters()) if isinstance(self, nn.Module) else []


class ControlComponent(ABC):
    """Mix-in for control components updated by global task loss.
    
    This is an alias for AntithesisComponent to clarify the role
    of control mechanisms like gating networks and state updates.
    """
    
    def get_control_parameters(self) -> List[nn.Parameter]:
        """Return parameters that should be updated by global loss."""
        return list(self.parameters()) if isinstance(self, nn.Module) else []


class HybridComponent(ABC):
    """Mix-in for components with both thesis and antithesis parameters.
    
    Some components may have both types of parameters that need
    different optimization objectives.
    """
    
    def get_thesis_parameters(self) -> List[nn.Parameter]:
        """Return thesis parameters updated by local loss."""
        raise NotImplementedError("Hybrid components must implement parameter separation")
    
    def get_antithesis_parameters(self) -> List[nn.Parameter]:
        """Return antithesis parameters updated by global loss."""
        raise NotImplementedError("Hybrid components must implement parameter separation")
