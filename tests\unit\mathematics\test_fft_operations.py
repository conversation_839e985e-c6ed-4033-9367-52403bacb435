"""
Comprehensive test suite for FFT operations in DELTA architecture.

This module tests the frequency-domain synthesis operations that are core to 
the DELTA block's Stage 3 synthesis:
V_S,raw = real(iFFT(FFT(V_T) ⊙ FFT(κ)))

Tests follow TDD principles and validate specification adherence.
"""

import pytest
import torch
import numpy as np
from typing import Tuple, Optional
import math

from source.delta.math.fft_ops import (
    circular_convolution_fft,
    windowed_fft_convolution,
    fft_magnitude_phase,
    apply_frequency_mask,
    frequency_domain_attention,
    spectral_regularization,
    adaptive_frequency_filtering,
    FFTConvolution,
    ensure_numerical_stability
)


class TestCircularConvolutionFFT:
    """Test the core synthesis operation: V_S,raw = real(iFFT(FFT(V_T) ⊙ FFT(κ)))"""
    
    def setup_method(self):
        """Set up test fixtures."""
        torch.manual_seed(42)
        self.batch_size = 2
        self.d_model = 64
        self.device = torch.device('cpu')
        
        # Create test thesis and antithesis vectors
        self.thesis = torch.randn(self.batch_size, self.d_model, dtype=torch.float32)
        self.antithesis = torch.randn(self.batch_size, self.d_model, dtype=torch.float32)
    
    def test_synthesis_equation_exact_match(self):
        """Test that implementation exactly matches specification equation."""
        # Manual implementation of the specification equation
        thesis_complex = self.thesis.to(torch.complex64)
        antithesis_complex = self.antithesis.to(torch.complex64)
        
        thesis_fft = torch.fft.fft(thesis_complex, dim=-1, norm="backward")
        antithesis_fft = torch.fft.fft(antithesis_complex, dim=-1, norm="backward")
        synthesis_fft = thesis_fft * antithesis_fft  # Hadamard product (⊙)
        expected = torch.fft.ifft(synthesis_fft, dim=-1, norm="backward").real
        
        # Test our implementation (with matching normalization)
        result = circular_convolution_fft(self.thesis, self.antithesis, norm="backward")
        
        torch.testing.assert_close(result, expected, rtol=1e-6, atol=1e-6)
    
    def test_output_shape_preservation(self):
        """Test that output shape matches input shape exactly."""
        result = circular_convolution_fft(self.thesis, self.antithesis)
        assert result.shape == self.thesis.shape == self.antithesis.shape
        assert result.dtype == torch.float32
    
    def test_convolution_property_validation(self):
        """Test that operation satisfies convolution theorem."""
        # Convolution theorem: F{f*g} = F{f} ⊙ F{g}
        result = circular_convolution_fft(self.thesis, self.antithesis, norm="backward")
        
        # Verify it's actually performing circular convolution
        # For circular convolution, the result should be periodic
        result_fft = torch.fft.fft(result.to(torch.complex64), dim=-1, norm="backward")
        expected_fft = (torch.fft.fft(self.thesis.to(torch.complex64), dim=-1, norm="backward") * 
                       torch.fft.fft(self.antithesis.to(torch.complex64), dim=-1, norm="backward"))
        
        torch.testing.assert_close(result_fft, expected_fft, rtol=1e-5, atol=1e-5)
    
    def test_normalization_modes(self):
        """Test different FFT normalization modes."""
        modes = ["forward", "backward", "ortho"]
        results = {}
        
        for mode in modes:
            results[mode] = circular_convolution_fft(
                self.thesis, self.antithesis, norm=mode
            )
        
        # Results should be different for different normalization modes
        assert not torch.allclose(results["forward"], results["backward"])
        assert not torch.allclose(results["forward"], results["ortho"])
    
    def test_batch_processing(self):
        """Test that batch processing works correctly."""
        # Test single sample
        single_thesis = self.thesis[0:1]
        single_antithesis = self.antithesis[0:1]
        single_result = circular_convolution_fft(single_thesis, single_antithesis)
        
        # Test full batch
        batch_result = circular_convolution_fft(self.thesis, self.antithesis)
        
        # First sample should match
        torch.testing.assert_close(
            batch_result[0:1], single_result, rtol=1e-6, atol=1e-6
        )
    
    def test_real_output_guarantee(self):
        """Test that output is always real-valued as specified."""
        result = circular_convolution_fft(self.thesis, self.antithesis)
        assert result.dtype == torch.float32
        assert not torch.is_complex(result)
    
    def test_numerical_stability_edge_cases(self):
        """Test stability with edge case inputs."""
        # Test with zeros
        zeros = torch.zeros_like(self.thesis)
        result_zeros = circular_convolution_fft(zeros, self.antithesis)
        assert torch.allclose(result_zeros, torch.zeros_like(result_zeros))
        
        # Test with very small values
        small_values = torch.full_like(self.thesis, 1e-8)
        result_small = circular_convolution_fft(small_values, small_values)
        assert torch.isfinite(result_small).all()
        
        # Test with very large values
        large_values = torch.full_like(self.thesis, 1e6)
        result_large = circular_convolution_fft(large_values, large_values)
        assert torch.isfinite(result_large).all()
    
    def test_linearity_property(self):
        """Test that convolution respects linearity."""
        alpha, beta = 2.5, 3.7
        
        # Linear combination of inputs
        linear_thesis = alpha * self.thesis
        linear_antithesis = beta * self.antithesis
        result_linear = circular_convolution_fft(linear_thesis, linear_antithesis)
        
        # Should equal alpha * beta times original result
        result_original = circular_convolution_fft(self.thesis, self.antithesis)
        expected = alpha * beta * result_original
        
        torch.testing.assert_close(result_linear, expected, rtol=1e-5, atol=1e-5)


class TestWindowedFFTConvolution:
    """Test windowed FFT convolution for enhanced spectral properties."""
    
    def setup_method(self):
        """Set up test fixtures."""
        torch.manual_seed(42)
        self.batch_size = 2
        self.d_model = 64
        self.thesis = torch.randn(self.batch_size, self.d_model)
        self.antithesis = torch.randn(self.batch_size, self.d_model)
    
    def test_window_types(self):
        """Test different window types produce different results."""
        window_types = ["hann", "hamming", "blackman"]
        results = {}
        
        for window_type in window_types:
            results[window_type] = windowed_fft_convolution(
                self.thesis, self.antithesis, window_type=window_type
            )
        
        # Different windows should produce different results
        assert not torch.allclose(results["hann"], results["hamming"])
        assert not torch.allclose(results["hann"], results["blackman"])
    
    def test_no_window_equivalence(self):
        """Test that no windowing equals standard FFT convolution."""
        windowed_result = windowed_fft_convolution(
            self.thesis, self.antithesis, window_type=None
        )
        standard_result = circular_convolution_fft(self.thesis, self.antithesis)
        
        torch.testing.assert_close(windowed_result, standard_result, rtol=1e-6, atol=1e-6)
    
    def test_window_effect_on_spectrum(self):
        """Test that windowing affects frequency spectrum as expected."""
        # Without window
        no_window = windowed_fft_convolution(
            self.thesis, self.antithesis, window_type=None
        )
        
        # With Hann window
        with_window = windowed_fft_convolution(
            self.thesis, self.antithesis, window_type="hann"
        )
        
        # Results should be different
        assert not torch.allclose(no_window, with_window)
        
        # Windowed version should have reduced spectral leakage
        no_window_fft = torch.fft.fft(no_window.to(torch.complex64))
        windowed_fft = torch.fft.fft(with_window.to(torch.complex64))
        
        # High frequency content should generally be reduced with windowing
        high_freq_mask = torch.arange(self.d_model) > self.d_model // 2
        no_window_high = torch.abs(no_window_fft[:, high_freq_mask]).mean()
        windowed_high = torch.abs(windowed_fft[:, high_freq_mask]).mean()
        
        # This is a general expectation, not a strict requirement
        # assert windowed_high <= no_window_high * 1.1  # Allow some tolerance


class TestFFTConvolutionClass:
    """Test the FFTConvolution module class."""
    
    def setup_method(self):
        """Set up test fixtures."""
        torch.manual_seed(42)
        self.d_model = 64
        self.fft_conv = FFTConvolution(d_model=self.d_model)
        self.thesis = torch.randn(2, self.d_model)
        self.antithesis = torch.randn(2, self.d_model)
    
    def test_module_forward_pass(self):
        """Test the forward pass of FFTConvolution module."""
        result = self.fft_conv(self.thesis)
        
        assert result.shape == self.thesis.shape
        assert result.dtype == torch.float32
        assert torch.isfinite(result).all()
    
    def test_module_parameters(self):
        """Test that module has learnable parameters if configured."""
        # Check if module has the expected parameters
        param_count = sum(p.numel() for p in self.fft_conv.parameters())
        
        # FFTConvolution should have freq_kernel_real, freq_kernel_imag, and bias
        expected_params = self.d_model * 3  # real + imag + bias
        assert param_count == expected_params
        
        # Check specific parameters exist
        assert hasattr(self.fft_conv, 'freq_kernel_real')
        assert hasattr(self.fft_conv, 'freq_kernel_imag')
        assert hasattr(self.fft_conv, 'bias')
    
    def test_gradient_flow(self):
        """Test that gradients flow through the FFT convolution."""
        self.thesis.requires_grad_(True)
        
        result = self.fft_conv(self.thesis)
        loss = result.sum()
        loss.backward()
        
        assert self.thesis.grad is not None
        assert torch.isfinite(self.thesis.grad).all()
        
        # Check module parameter gradients
        assert self.fft_conv.freq_kernel_real.grad is not None
        assert self.fft_conv.freq_kernel_imag.grad is not None
        assert self.fft_conv.bias.grad is not None


class TestSpectralOperations:
    """Test additional spectral operations for FFT-based synthesis."""
    
    def setup_method(self):
        """Set up test fixtures."""
        torch.manual_seed(42)
        self.batch_size = 2
        self.d_model = 64
        self.signal = torch.randn(self.batch_size, self.d_model)
    
    def test_fft_magnitude_phase(self):
        """Test magnitude and phase extraction from FFT."""
        magnitude, phase = fft_magnitude_phase(self.signal)
        
        assert magnitude.shape == self.signal.shape
        assert phase.shape == self.signal.shape
        assert (magnitude >= 0).all()  # Magnitude should be non-negative
        assert (phase >= -math.pi).all() and (phase <= math.pi).all()
    
    def test_frequency_mask_application(self):
        """Test frequency domain masking."""
        # Create a simple mask (keep low frequencies, zero high frequencies)
        mask = torch.ones(self.d_model)
        mask[self.d_model//2:] = 0
        
        masked_signal = apply_frequency_mask(self.signal, mask)
        
        assert masked_signal.shape == self.signal.shape
        
        # Check that high frequencies are indeed reduced
        original_fft = torch.fft.fft(self.signal.to(torch.complex64))
        masked_fft = torch.fft.fft(masked_signal.to(torch.complex64))
        
        # High frequency components should be significantly reduced
        high_freq_original = torch.abs(original_fft[:, self.d_model//2:]).mean()
        high_freq_masked = torch.abs(masked_fft[:, self.d_model//2:]).mean()
        
        assert high_freq_masked < high_freq_original
    
    def test_spectral_regularization(self):
        """Test spectral regularization for stability."""
        reg_loss = spectral_regularization(self.signal, lambda_reg=0.1)
        
        # Should return a scalar loss value
        assert reg_loss.numel() == 1
        assert torch.isfinite(reg_loss).all()
        assert reg_loss >= 0  # Loss should be non-negative
        
        # Higher regularization should give higher loss
        higher_reg_loss = spectral_regularization(self.signal, lambda_reg=0.2)
        assert higher_reg_loss > reg_loss
    
    def test_numerical_stability_enforcement(self):
        """Test numerical stability checks and corrections."""
        # Create potentially unstable input
        unstable_signal = torch.full((self.batch_size, self.d_model), float('inf'))
        unstable_signal[unstable_signal == float('inf')] = 1e10  # Very large values
        
        stable_signal = ensure_numerical_stability(unstable_signal)
        
        assert torch.isfinite(stable_signal).all()
        assert stable_signal.shape == unstable_signal.shape
        
        # Test with NaN values
        nan_signal = self.signal.clone()
        nan_signal[0, 0] = float('nan')
        
        stable_nan = ensure_numerical_stability(nan_signal)
        assert torch.isfinite(stable_nan).all()


class TestFrequencyDomainAttention:
    """Test frequency-domain attention mechanisms."""
    
    def setup_method(self):
        """Set up test fixtures."""
        torch.manual_seed(42)
        self.batch_size = 2
        self.seq_len = 8
        self.d_model = 64
        
        self.query = torch.randn(self.batch_size, self.seq_len, self.d_model)
        self.key = torch.randn(self.batch_size, self.seq_len, self.d_model)
        self.value = torch.randn(self.batch_size, self.seq_len, self.d_model)
    
    def test_frequency_domain_attention_shape(self):
        """Test that frequency domain attention preserves shapes."""
        output, weights = frequency_domain_attention(self.query, self.key, self.value)
        
        assert output.shape == self.value.shape
        assert weights.shape == (self.batch_size, self.seq_len, self.seq_len)
    
    def test_attention_weights_properties(self):
        """Test that attention weights have proper properties."""
        output, weights = frequency_domain_attention(self.query, self.key, self.value)
        
        # Attention weights should sum to 1 across key dimension
        weight_sums = weights.sum(dim=-1)
        expected_sums = torch.ones_like(weight_sums)
        torch.testing.assert_close(weight_sums, expected_sums, rtol=1e-5, atol=1e-5)
        
        # Weights should be non-negative
        assert (weights >= 0).all()
    
    def test_frequency_filtering_effect(self):
        """Test that frequency domain processing affects the output."""
        # Standard attention
        standard_output, _ = frequency_domain_attention(
            self.query, self.key, self.value, use_frequency_filter=False
        )
        
        # Frequency-filtered attention
        filtered_output, _ = frequency_domain_attention(
            self.query, self.key, self.value, use_frequency_filter=True
        )
        
        # Results should be different when filtering is applied
        assert not torch.allclose(standard_output, filtered_output, rtol=1e-5)


class TestAdaptiveFrequencyFiltering:
    """Test adaptive frequency filtering for dynamic spectral control."""
    
    def setup_method(self):
        """Set up test fixtures."""
        torch.manual_seed(42)
        self.batch_size = 2
        self.d_model = 64
        self.signal = torch.randn(self.batch_size, self.d_model)
    
    def test_adaptive_filtering_output_shape(self):
        """Test that adaptive filtering preserves input shape."""
        filtered_signal = adaptive_frequency_filtering(self.signal)
        assert filtered_signal.shape == self.signal.shape
    
    def test_adaptive_filtering_stability(self):
        """Test that adaptive filtering maintains numerical stability."""
        filtered_signal = adaptive_frequency_filtering(self.signal)
        
        assert torch.isfinite(filtered_signal).all()
        assert not torch.isnan(filtered_signal).any()
    
    def test_filtering_intensity_control(self):
        """Test that filtering intensity can be controlled."""
        # Light filtering
        light_filtered = adaptive_frequency_filtering(self.signal, intensity=0.1)
        
        # Heavy filtering
        heavy_filtered = adaptive_frequency_filtering(self.signal, intensity=0.9)
        
        # Different intensities should produce different results
        assert not torch.allclose(light_filtered, heavy_filtered, rtol=1e-3)
        
        # Light filtering should be closer to original
        light_diff = torch.norm(self.signal - light_filtered)
        heavy_diff = torch.norm(self.signal - heavy_filtered)
        
        assert light_diff <= heavy_diff


class TestFFTOperationsIntegration:
    """Integration tests for FFT operations in DELTA context."""
    
    def setup_method(self):
        """Set up test fixtures for integration testing."""
        torch.manual_seed(42)
        self.batch_size = 4
        self.d_model = 128
        self.seq_len = 16
        
        # Simulate DELTA block thesis and antithesis
        self.thesis = torch.randn(self.batch_size, self.d_model)
        self.antithesis = torch.randn(self.batch_size, self.d_model)
    
    def test_delta_synthesis_pipeline(self):
        """Test the complete synthesis pipeline as used in DELTA."""
        # Stage 3: Frequency-Domain Synthesis (from specification)
        synthesis_raw = circular_convolution_fft(self.thesis, self.antithesis)
        
        # Verify this produces reasonable output for next stage
        assert synthesis_raw.shape == (self.batch_size, self.d_model)
        assert torch.isfinite(synthesis_raw).all()
        
        # Should not be all zeros (unless inputs are specially crafted)
        assert not torch.allclose(synthesis_raw, torch.zeros_like(synthesis_raw))
    
    def test_synthesis_with_different_magnitudes(self):
        """Test synthesis robustness across different input magnitudes."""
        magnitudes = [1e-3, 1e-1, 1.0, 1e1, 1e3]
        
        for mag in magnitudes:
            scaled_thesis = self.thesis * mag
            scaled_antithesis = self.antithesis * mag
            
            result = circular_convolution_fft(scaled_thesis, scaled_antithesis)
            
            # Should handle different magnitudes gracefully
            assert torch.isfinite(result).all()
            assert not torch.isnan(result).any()
    
    def test_gradient_flow_through_synthesis(self):
        """Test that gradients flow correctly through synthesis operation."""
        thesis = self.thesis.clone().requires_grad_(True)
        antithesis = self.antithesis.clone().requires_grad_(True)
        
        synthesis = circular_convolution_fft(thesis, antithesis)
        loss = synthesis.sum()
        loss.backward()
        
        # Gradients should exist and be finite
        assert thesis.grad is not None
        assert antithesis.grad is not None
        assert torch.isfinite(thesis.grad).all()
        assert torch.isfinite(antithesis.grad).all()
        
        # Gradients should not be all zeros (unless in degenerate cases)
        assert not torch.allclose(thesis.grad, torch.zeros_like(thesis.grad))
        assert not torch.allclose(antithesis.grad, torch.zeros_like(antithesis.grad))


if __name__ == "__main__":
    pytest.main([__file__])
