"""Complete DELTA Block Implementation.

This module assembles the four stages of the DELTA block:
1. Thesis Generation
2. Antithesis Generation
3. Frequency-Domain Synthesis
4. Refinement and State Update

It handles both the prefill and decoding operational phases.
"""

import torch
import torch.nn as nn
from torch import Tensor
from typing import Optional, List, Tuple, Dict, Union
import logging

from .base.abstract_implementations import Abstract<PERSON><PERSON><PERSON><PERSON>
from .base.types import DELT<PERSON><PERSON><PERSON>utput, KappaCache, DELTAConfig
from .base.mixins import ThesisComponent, AntithesisComponent
from .attractors.gating_networks import DualGatingNetwork
from .attractors.structural_attractors import StructuralAttractorBank
from .attractors.semantic_attractors import SemanticAttractorBank
from ..math.convolution import circular_convolve
from ..components.normalization import LayerNorm
from ..math.tensor_ops import safe_concat
from ..math.advanced.numerical_stability import stable_sigmoid, clip_by_value, check_numerical_stability, ensure_numerical_stability
from ..components.ffn import FeedForwardNetwork
from ..components.attention import KappaCacheAttention
from ..utils.constants import EPSILON, INIT_RANGE, UPDATE_GATE_BIAS_INIT

# Set up module logger
logger = logging.getLogger(__name__)


class ThesisProjection(nn.Module, ThesisComponent):
    """Thesis projection layer with proper component categorization."""
    
    def __init__(self, d_model: int, bias: bool = True, dropout: float = 0.0):
        super().__init__()
        self.projection = nn.Linear(d_model, d_model, bias=bias)
        self.dropout = nn.Dropout(dropout) if dropout > 0.0 else nn.Identity()
        
        # Initialize weights
        nn.init.xavier_uniform_(self.projection.weight, gain=1.0)
        if bias:
            nn.init.zeros_(self.projection.bias)
    
    def forward(self, x):
        return self.projection(self.dropout(x))
    
    def get_thesis_parameters(self) -> List[nn.Parameter]:
        """Return thesis parameters for dual-objective learning."""
        return list(self.projection.parameters())


class DELTABlock(AbstractDELTABlock):
    """A single DELTA block that performs a full dialectic cycle.

    This class integrates all four stages of the DELTA process and manages
    the data flow for both prefill (local antithesis) and decoding
    (effective antithesis using κ-cache attention).

    Args:
        config: A DELTAConfig object containing model hyperparameters.
    """
    def __init__(self, config: DELTAConfig):
        super().__init__(config)
        self.d_model = config.d_model

        # Stage 1: Thesis Generation - W_T and b_T (Algorithm 1, Line 2)
        self.thesis_projection = ThesisProjection(
            d_model=config.d_model,
            bias=True,
            dropout=config.dropout if getattr(config, 'thesis_dropout_enabled', False) else 0.0
        )

        # Stage 2: Antithesis Generation (integrated components)
        # Gating networks for both domains (Algorithm 1, Lines 5-6)
        self.gating_network = DualGatingNetwork(
            d_model=config.d_model,
            num_attractors=config.n_attractors,
            dropout=config.dropout
        )

        # Structural attractor bank (stateless) (Algorithm 1, Lines 9-12)
        self.structural_attractors = StructuralAttractorBank(
            d_model=config.d_model,
            num_attractors=config.n_attractors,
            projection_size=config.structural_proj_size,
            dropout=config.dropout
        )

        # Semantic attractor bank (stateful) (Algorithm 1, Lines 15-19)
        # Per spec, forces should be f_sem,i = tanh(h_sem,out_i), so hidden_size should equal d_model
        semantic_hidden_size = config.semantic_hidden_size if hasattr(config, 'semantic_hidden_size') and config.semantic_hidden_size != config.d_model else None
        self.semantic_attractors = SemanticAttractorBank(
            d_model=config.d_model,
            num_attractors=config.n_attractors,
            hidden_size=semantic_hidden_size,  # Defaults to d_model for spec compliance
            cell_type=config.semantic_cell_type if hasattr(config, 'semantic_cell_type') else "gru",
            dropout=config.dropout
        )

        # κ-cache attention for decoding phase (Section 4.2)
        self.kappa_cache_attention = KappaCacheAttention(d_model=config.d_model)

        # Stage 3: Frequency-Domain Synthesis - no parameters needed
        # Uses circular_convolve function directly

        # Stage 4: Refinement and State Update (Algorithm 1, Lines 26-29)
        self.norm1 = LayerNorm(config.d_model, eps=config.layer_norm_eps)
        self.ffn = FeedForwardNetwork(
            d_model=config.d_model,
            d_ff=config.d_model * config.ffn_expansion_ratio,
            activation=config.ffn_activation,
            dropout=config.dropout
        )
        self.norm2 = LayerNorm(config.d_model, eps=config.layer_norm_eps)
        self.update_gate = nn.Linear(config.d_model * 2, config.d_model)
        
        # Apply spectral normalization to update gate for stability
        self.update_gate = nn.utils.spectral_norm(self.update_gate)

        # Optional learned summary projection
        if getattr(config, 'thesis_summary_function', 'identity') == 'learned':
            self.summary_projection = nn.Linear(config.d_model, config.d_model, bias=False)
            nn.init.xavier_uniform_(self.summary_projection.weight, gain=1.0)

        # TODO: Mutation Phase Support (Section 4.3 of DeltaDecoder.md)
        # The Mutation Phase requires:
        # 1. Trajectory storage mechanism for successful generations
        # 2. Essence extraction function: E(V_s, h_t, s, L) = (C_ext, B_ext, F_ext)
        # 3. Mutation integration mechanism to update attractor states
        # This is planned for future implementation
        self._mutation_enabled = False  # Flag for future mutation phase support

        # Initialize weights
        self._initialize_weights()

    def _initialize_weights(self) -> None:
        """Initialize all weights for the integrated DELTA block."""
        # Stage 1: Thesis projection initializes itself
        self._verify_component_initialization("thesis_projection", self.thesis_projection)

        # Stage 2: Antithesis generation components initialize themselves
        # Verify each component after it initializes
        self._verify_component_initialization("gating_network", self.gating_network)
        self._verify_component_initialization("structural_attractors", self.structural_attractors)
        self._verify_component_initialization("semantic_attractors", self.semantic_attractors)
        self._verify_component_initialization("kappa_cache_attention", self.kappa_cache_attention)

        # Stage 4: Refinement and state update
        # LayerNorm components initialize themselves with proper defaults
        # FFN initialization handled by FeedForwardNetwork component
        self._verify_component_initialization("norm1", self.norm1)
        self._verify_component_initialization("ffn", self.ffn)
        self._verify_component_initialization("norm2", self.norm2)

        # Update gate initialization
        try:
            nn.init.xavier_uniform_(self.update_gate.weight)
            nn.init.constant_(self.update_gate.bias, UPDATE_GATE_BIAS_INIT)
            self._verify_component_initialization("update_gate", self.update_gate)
        except Exception as e:
            raise RuntimeError(f"Failed to initialize update gate: {str(e)}")

        # Final comprehensive verification
        self._verify_all_parameters()

    def _verify_component_initialization(self, component_name: str, component: nn.Module) -> None:
        """Verify that a specific component has been properly initialized.
        
        Args:
            component_name: Name of the component for error reporting
            component: The module to verify
        """
        # Guard expensive check behind debug logging level
        if not logger.isEnabledFor(logging.DEBUG):
            return
            
        for name, param in component.named_parameters():
            if not torch.isfinite(param).all():
                raise RuntimeError(
                    f"Component '{component_name}' parameter '{name}' contains non-finite values "
                    f"after initialization. Shape: {param.shape}, "
                    f"Contains NaN: {torch.isnan(param).any().item()}, "
                    f"Contains Inf: {torch.isinf(param).any().item()}"
                )

    def _verify_all_parameters(self) -> None:
        """Verify that all parameters in the module have been properly initialized."""
        for name, param in self.named_parameters():
            if not torch.isfinite(param).all():
                raise RuntimeError(
                    f"Parameter '{name}' contains non-finite values after full initialization. "
                    f"Shape: {param.shape}, dtype: {param.dtype}, "
                    f"Contains NaN: {torch.isnan(param).any().item()}, "
                    f"Contains Inf: {torch.isinf(param).any().item()}"
                )

    def _validate_inputs(self, input_state: Tensor, semantic_states: Optional[Dict[int, Tensor]] = None,
                        kappa_cache: Optional[KappaCache] = None, layer_idx: Optional[int] = None) -> None:
        """Validate input tensors and parameters."""
        # Validate input state
        if input_state.dim() != 2:
            raise ValueError(
                f"Expected input_state to be 2D tensor with shape (batch_size, d_model), "
                f"but got {input_state.dim()}D tensor with shape {input_state.shape}"
            )
        if input_state.shape[-1] != self.d_model:
            raise ValueError(
                f"Expected input_state last dimension to be {self.d_model}, "
                f"but got {input_state.shape[-1]}. Full shape: {input_state.shape}"
            )

        # Validate dtype and device consistency
        if not input_state.is_floating_point():
            raise TypeError(
                f"Expected input_state to be floating point tensor, "
                f"but got {input_state.dtype}"
            )
        
        base_device = input_state.device
        base_dtype = input_state.dtype

        # Validate semantic states if provided
        if semantic_states is not None:
            if not isinstance(semantic_states, dict):
                raise TypeError(
                    f"Expected semantic_states to be Dict[int, Tensor], "
                    f"but got {type(semantic_states).__name__}"
                )
            if len(semantic_states) != self.config.n_attractors:
                raise ValueError(
                    f"Expected {self.config.n_attractors} semantic states, "
                    f"but got {len(semantic_states)}. Keys provided: {list(semantic_states.keys())}"
                )
            # Validate that keys are consecutive integers starting from 0
            expected_keys = set(range(self.config.n_attractors))
            actual_keys = set(semantic_states.keys())
            if actual_keys != expected_keys:
                missing_keys = expected_keys - actual_keys
                extra_keys = actual_keys - expected_keys
                raise ValueError(
                    f"Invalid semantic_states keys. "
                    f"Expected: {sorted(expected_keys)}, "
                    f"Got: {sorted(actual_keys)}. "
                    f"Missing: {sorted(missing_keys) if missing_keys else 'None'}, "
                    f"Extra: {sorted(extra_keys) if extra_keys else 'None'}"
                )
            
            # Validate device and dtype alignment
            for idx, state in semantic_states.items():
                if state.device != base_device:
                    raise ValueError(
                        f"Device mismatch: input_state on {base_device}, "
                        f"but semantic_states[{idx}] on {state.device}"
                    )
                if state.dtype != base_dtype:
                    raise ValueError(
                        f"Dtype mismatch: input_state has {base_dtype}, "
                        f"but semantic_states[{idx}] has {state.dtype}"
                    )

        # Validate kappa_cache and layer_idx consistency
        if (kappa_cache is not None) != (layer_idx is not None):
            raise ValueError(
                "kappa_cache and layer_idx must be provided together (Decoding Phase) "
                "or not at all (Prefill Phase). "
                f"Got kappa_cache: {'provided' if kappa_cache is not None else 'None'}, layer_idx: {layer_idx}"
            )
        
        # Validate kappa_cache device alignment if provided
        if kappa_cache is not None:
            layer_cache = kappa_cache.get_cache_for_layer(layer_idx)
            if layer_cache.device != base_device:
                raise ValueError(
                    f"Device mismatch: input_state on {base_device}, "
                    f"but kappa_cache on {layer_cache.device}"
                )

    def _convert_semantic_states_to_dict(self, h_sem_list: List[Tensor]) -> Dict[int, Tensor]:
        """Convert semantic states from List[Tensor] to Dict[int, Tensor] format."""
        return {i: state for i, state in enumerate(h_sem_list)}

    def _convert_semantic_states_to_list(self, h_sem_dict: Dict[int, Tensor]) -> List[Tensor]:
        """Convert semantic states from Dict[int, Tensor] to List[Tensor] format."""
        return [h_sem_dict[i] for i in range(len(h_sem_dict))]

    def _generate_thesis(self, h_in: Tensor) -> Tensor:
        """Stage 1: Thesis Generation (Algorithm 1, Line 2).

        Mathematical formulation: V_T = W_T * h_in + b_T

        Args:
            h_in: Input state tensor of shape (batch_size, d_model)

        Returns:
            V_T: Thesis proposal tensor of same shape as input
        """
        # Apply projection with integrated dropout
        V_T = self.thesis_projection(h_in)
        return V_T

    def _frequency_domain_synthesis(self, thesis: Tensor, antithesis: Tensor) -> Tensor:
        """Stage 3: Frequency-Domain Synthesis (Algorithm 1, Line 24).

        Mathematical formulation: V_S,raw = real(iFFT(FFT(V_T) ⊙ FFT(κ)))

        Args:
            thesis: The proposal vector (V_T) of shape (batch_size, d_model)
            antithesis: The constraint vector (κ) of shape (batch_size, d_model)

        Returns:
            The raw synthesis vector (V_S,raw) of shape (batch_size, d_model)
        """
        # Ensure inputs have the same shape
        if thesis.shape != antithesis.shape:
            raise ValueError(
                f"Thesis and Antithesis must have the same shape, "
                f"but got {thesis.shape} and {antithesis.shape}"
            )

        # Perform circular convolution using FFT with numerical stability
        # V_S,raw = real(iFFT(FFT(V_T) * FFT(κ)))
        try:
            synthesis_raw = circular_convolve(thesis, antithesis)
            # Apply numerical stability check to prevent overflow
            synthesis_raw = ensure_numerical_stability(synthesis_raw, "frequency_domain_synthesis")
            return synthesis_raw
        except Exception as e:
            logger.warning(f"FFT synthesis failed, falling back to element-wise: {e}")
            # Fallback to element-wise product if FFT fails
            return thesis * antithesis

    def _refinement_and_state_update_with_details(
        self, thesis: Tensor, synthesis_raw: Tensor, input_state: Tensor
    ) -> Tuple[Tensor, Tensor]:
        """Stage 4: Refinement and State Update with detailed outputs.

        Args:
            thesis: The thesis vector (V_T) of shape (batch_size, d_model)
            synthesis_raw: The raw synthesis vector (V_S,raw) from Stage 3
            input_state: The initial input state to the block

        Returns:
            Tuple of (final_output_state, synthesis_refined)
            Both tensors have shape (batch_size, d_model)
        """
        # Algorithm 1, Line 26: h' ← LayerNorm(V_T + V_S,raw)
        h_prime = self.norm1(thesis + synthesis_raw)

        # Algorithm 1, Line 27: V_S ← LayerNorm(h' + FFN(h'))
        # FFN computation using reusable component
        ffn_out = self.ffn(h_prime)
        synthesis_refined = self.norm2(h_prime + ffn_out)

        # Algorithm 1, Line 28: z ← σ(W_z [V_S; input_state] + b_z)
        gate_input = safe_concat([synthesis_refined, input_state], dim=-1)
        z = stable_sigmoid(self.update_gate(gate_input))

        # Algorithm 1, Line 29: h_out ← (1-z) ⊙ input_state + z ⊙ V_S
        h_out = (1 - z) * input_state + z * synthesis_refined

        return h_out, synthesis_refined

    def _collect_detailed_outputs(
        self, thesis: Tensor, antithesis: Tensor, h_sem_in: Optional[List[Tensor]], 
        cached_outputs: Optional[Dict[str, Union[Tensor, List[Tensor]]]] = None
    ) -> Dict[str, Union[Tensor, List[Tensor]]]:
        """Collect detailed outputs for analysis when return_details=True.
        
        This method uses cached values from the main forward pass to avoid recomputation
        when return_details=True. Only falls back to recomputation if cached_outputs is None.
        
        Args:
            thesis: Thesis vector (V_T)
            antithesis: Antithesis vector (κ)
            h_sem_in: Input semantic states
            cached_outputs: Pre-computed outputs from main pass to avoid recomputation
            
        Returns:
            Dictionary containing detailed intermediate outputs
        """
        detailed: Dict[str, Union[Tensor, List[Tensor]]] = {}

        try:
            # Use cached outputs if available (optimization to avoid recomputation)
            if cached_outputs is not None:
                detailed.update(cached_outputs)
            else:
                # Fallback: Re-compute with details (only used for debugging/analysis)
                logger.warning("Cached outputs not provided, falling back to recomputation")
                summary_vector = self._summarize_thesis(thesis)
                structural_gates, semantic_gates = self.gating_network(summary_vector)

                # Get individual structural forces
                structural_forces = self.structural_attractors(thesis)
                detailed['structural_forces'] = structural_forces
                detailed['gate_weights_struct'] = structural_gates

                # Get individual semantic forces if states available
                if h_sem_in is not None:
                    semantic_forces, _ = self.semantic_attractors(summary_vector, h_sem_in)
                    detailed['semantic_forces'] = semantic_forces
                    detailed['gate_weights_sem'] = semantic_gates

        except RuntimeError as e:
            # Log specific runtime errors (e.g., CUDA out of memory)
            logger.warning(
                f"Failed to collect detailed outputs due to runtime error: {str(e)}. "
                "This may occur during memory-constrained inference."
            )
        except Exception as e:
            # Log unexpected errors
            logger.error(
                f"Unexpected error while collecting detailed outputs: {type(e).__name__}: {str(e)}. "
                "Continuing with empty detailed outputs."
            )

        return detailed

    def _summarize_thesis(self, thesis: Tensor) -> Tensor:
        """Compute summary vector from thesis with configurable strategy.

        Per DeltaDecoder.md Section 3.1.2, the summarize function can use
        different strategies based on configuration.

        Args:
            thesis: Thesis vector (V_T) of shape (batch_size, d_model)

        Returns:
            Summary vector (v_summary) of shape (batch_size, d_model)
        """
        strategy = getattr(self.config, 'thesis_summary_function', 'identity')
        
        if strategy == "identity":
            # Identity function as specified for single-token processing
            return thesis
        elif strategy == "mean":
            # For batch processing, take mean across batch dimension
            return torch.mean(thesis, dim=0, keepdim=True).expand_as(thesis)
        elif strategy == "max":
            # Take max across batch dimension
            max_vals, _ = torch.max(thesis, dim=0, keepdim=True)
            return max_vals.expand_as(thesis)
        elif strategy == "learned":
            # Use a learned projection (would need to be initialized in __init__)
            if not hasattr(self, 'summary_projection'):
                # Fallback to identity if not initialized
                logger.warning("Learned summary function requested but not initialized, using identity")
                return thesis
            return self.summary_projection(thesis)
        else:
            logger.warning(f"Unknown thesis summary function '{strategy}', using identity")
            return thesis

    def _generate_local_antithesis(
        self, thesis: Tensor, h_sem_in: Optional[List[Tensor]] = None
    ) -> Tuple[Tensor, List[Tensor]]:
        """Generate local antithesis using integrated attractor components.

        Implements Algorithm 1, lines 4-22:
        - Compute summary vector and gating weights
        - Generate structural and semantic forces
        - Combine into composite antithesis κ

        Reference: Algorithm 1, lines 4-22

        Args:
            thesis: Thesis vector (V_T) of shape (batch_size, d_model)
            h_sem_in: List of semantic attractor hidden states

        Returns:
            Tuple of (composite_antithesis_κ, updated_semantic_states)
        """
        batch_size = thesis.shape[0]

        # Algorithm 1, Line 4: Compute summary vector
        summary_vector = self._summarize_thesis(thesis)

        # Algorithm 1, Lines 5-6: Compute gating weights
        structural_gates, semantic_gates = self.gating_network(summary_vector)

        # Algorithm 1, Lines 9-12: Compute structural forces
        structural_forces = self.structural_attractors(thesis)
        kappa_struct = self.structural_attractors.compute_weighted_sum(
            structural_forces, structural_gates
        )

        # Initialize semantic states if not provided
        if h_sem_in is None:
            h_sem_in = self.semantic_attractors.reset_state(batch_size)

        # Algorithm 1, Lines 15-19: Compute semantic forces with state updates
        semantic_forces, h_sem_out = self.semantic_attractors(summary_vector, h_sem_in)
        kappa_sem = self.semantic_attractors.compute_weighted_sum(
            semantic_forces, semantic_gates
        )

        # Algorithm 1, Line 22: Composite antithesis
        kappa = kappa_struct + kappa_sem

        return kappa, h_sem_out

    # TODO: Mutation Phase Methods (Section 4.3 of DeltaDecoder.md)
    # The following methods are stubs for future implementation of the Mutation Phase
    
    def extract_essence(
        self, 
        synthesis_vectors: List[Tensor], 
        states: List[Tensor], 
        trajectory_info: Dict[str, Union[int, float]]
    ) -> Optional[Dict[str, Tensor]]:
        """Extract essence from a successful generation trajectory.
        
        This method would implement the Essence function E(V_s, h_t, s, L) 
        described in Section 4.3 of the specification.
        
        Args:
            synthesis_vectors: List of synthesis vectors from the trajectory
            states: List of states from the trajectory
            trajectory_info: Metadata about the trajectory (score, length, etc.)
            
        Returns:
            Dictionary containing extracted patterns (C_ext, B_ext, F_ext) or None
            
        Note:
            This is a placeholder for future implementation. The Mutation Phase
            requires infrastructure for trajectory storage and evaluation that
            is not yet in place.
        """
        if not self._mutation_enabled:
            return None
            
        # TODO: Implement essence extraction logic
        # 1. Analyze synthesis vectors for common patterns
        # 2. Extract structural constraints (C_ext)
        # 3. Extract boundary conditions (B_ext)
        # 4. Extract force patterns (F_ext)
        raise NotImplementedError(
            "Mutation Phase essence extraction is planned for future implementation. "
            "See Section 4.3 of DeltaDecoder.md for specification details."
        )
    
    def apply_mutation(
        self, 
        essence: Dict[str, Tensor]
    ) -> None:
        """Apply extracted essence to update attractor states.
        
        This method would implement the mutation integration mechanism
        to incorporate learned patterns into the model's attractors.
        
        Args:
            essence: Dictionary containing extracted patterns from successful trajectories
            
        Note:
            This is a placeholder for future implementation.
        """
        if not self._mutation_enabled:
            return
            
        # TODO: Implement mutation application logic
        # 1. Update structural attractor parameters with C_ext
        # 2. Update semantic attractor states with B_ext
        # 3. Adjust force generation patterns with F_ext
        raise NotImplementedError(
            "Mutation Phase application is planned for future implementation. "
            "See Section 4.3 of DeltaDecoder.md for specification details."
        )

    def reset_semantic_states_for_mutation(self, batch_size: int, essence: Optional[Tensor] = None) -> Dict[int, Tensor]:
        """Reset semantic attractor states for Mutation Phase as per §4.3.
        
        Implements the horizon mutation mechanism where the extracted essence ε 
        is used to mutate the "reasoning horizon" embodied by the semantic attractor states.
        
        Per Algorithm in §4.3:
        h'_{sem,i,j} = h_{sem,i,j} ⊕ ε
        For simple additive fusion: h'_{sem,i,j} = (1 - λ) · h_{sem,i,j} + λ · ε
        
        Args:
            batch_size: Batch size for new states
            essence: Optional essence vector ε from successful trajectory (d_model,)
            
        Returns:
            Dictionary mapping attractor indices to new hidden states
        """
        # Get fresh reset states
        fresh_states = self.semantic_attractors.reset_state(batch_size)
        
        # If essence is provided, apply fusion operation
        if essence is not None:
            essence = essence.to(device=fresh_states[0].device, dtype=fresh_states[0].dtype)
            
            # Expand essence to batch size if needed
            if essence.dim() == 1:
                essence = essence.unsqueeze(0).expand(batch_size, -1)  # (batch_size, d_model)
            
            # Apply horizon mutation with learning rate λ = 0.1 (from mutation_rate)
            lambda_rate = self.config.mutation_rate
            
            mutated_states = []
            for state in fresh_states:
                # h'_{sem,i,j} = (1 - λ) · h_{sem,i,j} + λ · ε
                mutated_state = (1 - lambda_rate) * state + lambda_rate * essence
                mutated_states.append(mutated_state)
            
            return self._convert_semantic_states_to_dict(mutated_states)
        else:
            # Return fresh states without mutation
            return self._convert_semantic_states_to_dict(fresh_states)

    # Note: κ-cache attention logic moved to KappaCacheAttention component
    # to eliminate code duplication

    def forward(
        self,
        input_state: Tensor,
        semantic_states: Optional[Dict[int, Tensor]] = None,
        kappa_cache: Optional[KappaCache] = None,
        layer_idx: Optional[int] = None,
        return_details: bool = False
    ) -> DELTABlockOutput:
        """Executes a full forward pass of the DELTA block.

        The behavior changes based on the presence of `kappa_cache`:
        - If `kappa_cache` is None (Prefill Phase):
            - Generates a local antithesis (κ) based on the input.
            - The generated κ is stored for the cache.
        - If `kappa_cache` is not None (Decoding Phase):
            - Generates a local antithesis (κ_new).
            - Uses attention over the `kappa_cache` to get a contextual
              antithesis (κ_context).
            - Fuses them to create an effective antithesis (κ_effective).

        Args:
            input_state: Input state of shape (batch_size, d_model).
            semantic_states: Dict mapping attractor index to hidden state tensors.
            kappa_cache: The complete κ-cache tensor.
            layer_idx: The current layer's index, for accessing the cache.
            return_details: Whether to return detailed intermediate outputs.

        Returns:
            A DELTABlockOutput tuple containing the output state, updated
            semantic states, and the generated local antithesis.
        """
        # Validate inputs
        self._validate_inputs(input_state, semantic_states, kappa_cache, layer_idx)

        # Convert semantic states from Dict to List format for internal processing
        h_sem_in = None
        if semantic_states is not None:
            h_sem_in = self._convert_semantic_states_to_list(semantic_states)

        # ===== COMPLETE ALGORITHM 1 IMPLEMENTATION =====

        # Stage 1: Thesis Generation (Algorithm 1, Line 2)
        # V_T ← W_T * input_state + b_T
        thesis = self._generate_thesis(input_state)

        # Stage 2: Antithesis Generation (Algorithm 1, Lines 4-22)
        # This computes κ_new (Decoding) or the final κ (Prefill)
        local_antithesis, h_sem_out = self._generate_local_antithesis(thesis, h_sem_in)

        # Cache intermediate outputs for detailed analysis if needed
        # This optimization avoids recomputation in _collect_detailed_outputs
        cached_outputs = None
        if return_details:
            # Compute values that would otherwise be recomputed in _collect_detailed_outputs
            summary_vector = self._summarize_thesis(thesis)
            structural_gates, semantic_gates = self.gating_network(summary_vector)
            structural_forces = self.structural_attractors(thesis)
            
            cached_outputs = {
                'structural_forces': structural_forces,
                'gate_weights_struct': structural_gates,
            }
            
            if h_sem_in is not None:
                semantic_forces, _ = self.semantic_attractors(summary_vector, h_sem_in)
                cached_outputs['semantic_forces'] = semantic_forces
                cached_outputs['gate_weights_sem'] = semantic_gates

        # Determine operational phase and compute final antithesis
        if kappa_cache is not None and layer_idx is not None:
            # DECODING PHASE: Fuse local and cached antithesis (Section 4.2)
            layer_cache = kappa_cache.get_cache_for_layer(layer_idx)
            final_antithesis = self.kappa_cache_attention(
                v_thesis_new=thesis,
                kappa_new=local_antithesis,
                kappa_cache=layer_cache
            )
        else:
            # PREFILL PHASE: Use only the locally generated antithesis
            final_antithesis = local_antithesis

        # Stage 3: Frequency-Domain Synthesis (Algorithm 1, Line 24)
        # V_S,raw ← real(iFFT(FFT(V_T) ⊙ FFT(κ)))
        synthesis_raw = self._frequency_domain_synthesis(thesis, final_antithesis)

        # Stage 4: Refinement and State Update (Algorithm 1, Lines 26-29)
        # h' ← LayerNorm(V_T + V_S,raw)
        # V_S ← LayerNorm(h' + FFN(h'))
        # z ← σ(W_z [V_S; input_state] + b_z)
        # h_out ← (1-z) ⊙ input_state + z ⊙ V_S
        h_out, synthesis_refined = self._refinement_and_state_update_with_details(thesis, synthesis_raw, input_state)

        # Prepare detailed outputs if requested
        detailed_outputs: Dict[str, Union[Tensor, List[Tensor], None]] = {}
        if return_details:
            # Get detailed outputs using cached values to avoid recomputation
            detailed_outputs = self._collect_detailed_outputs(
                thesis, local_antithesis, h_sem_in, cached_outputs
            )

        return DELTABlockOutput(
            output_state=h_out,
            thesis=thesis,
            local_antithesis=local_antithesis,  # Always return local for caching
            kappa_effective=final_antithesis.detach() if kappa_cache is not None else final_antithesis,  # Detach for local loss
            synthesis_raw=synthesis_raw,
            synthesis_refined=synthesis_refined,  # Correct V_S from Algorithm 1, Line 27
            semantic_states=h_sem_out,  # Return as List[Tensor] for single layer
            # Optional detailed outputs
            structural_forces=detailed_outputs.get('structural_forces') if return_details else None,
            semantic_forces=detailed_outputs.get('semantic_forces') if return_details else None,
            gate_weights_struct=detailed_outputs.get('gate_weights_struct') if return_details else None,
            gate_weights_sem=detailed_outputs.get('gate_weights_sem') if return_details else None
        )

    def get_local_loss_parameters(self) -> List[nn.Parameter]:
        """Returns parameters updated by the local tension loss (Thesis only).

        Per Section 5 of DeltaDecoder.md, only thesis parameters are updated
        by the local tension loss: L_local = 1/2 * ||V_T - detach(κ_effective)||^2
        """
        params = []
        
        # Collect from thesis components using mix-in
        if hasattr(self.thesis_projection, 'get_thesis_parameters'):
            params.extend(self.thesis_projection.get_thesis_parameters())
        else:
            # Fallback for components without mix-in
            params.extend(self.thesis_projection.parameters())
            
        # Include learned summary projection if it exists
        if hasattr(self, 'summary_projection'):
            params.extend(self.summary_projection.parameters())
            
        return params

    def get_global_loss_parameters(self) -> List[nn.Parameter]:
        """Returns parameters updated by the global task loss (Antithesis/Control).

        This includes all components except the thesis generation:
        - Stage 2: Antithesis generation components
        - Stage 3: No parameters (pure function)
        - Stage 4: Refinement and state update components
        - κ-cache attention components
        """
        params = []
        # Stage 2: Antithesis generation
        params.extend(self.gating_network.parameters())
        params.extend(self.structural_attractors.parameters())
        params.extend(self.semantic_attractors.parameters())
        params.extend(self.kappa_cache_attention.parameters())

        # Stage 4: Refinement and state update
        params.extend(self.norm1.parameters())
        params.extend(self.ffn.parameters())
        params.extend(self.norm2.parameters())
        params.extend(self.update_gate.parameters())

        return params

    def reset_semantic_states(self, batch_size: int) -> Dict[int, Tensor]:
        """Reset all semantic attractor states.

        Args:
            batch_size: Batch size for initialization

        Returns:
            Dict mapping attractor index to initial semantic hidden states
        """
        states_list = self.semantic_attractors.reset_state(batch_size)
        return {i: state for i, state in enumerate(states_list)}
