"""FFT/iFFT operations for frequency-domain synthesis.

This module implements the frequency-domain convolution used in the
synthesis stage of DELTA blocks, along with related utilities.
"""

import torch
from torch import Tensor
import torch.nn.functional as F
from typing import Optional, Tuple

from ..utils.constants import FFT_NORM_MODE, EPSILON, FFT_WINDOW_TYPE


def create_window(
    size: int,
    window_type: str,
    device: torch.device
) -> Tensor:
    """Create a window function for FFT operations.
    
    Args:
        size: Window size
        window_type: Type of window ("hann", "hamming", "blackman")
        device: Device to create window on
        
    Returns:
        Window tensor of shape (size,)
    """
    if window_type == "hann":
        window = torch.hann_window(size, device=device)
    elif window_type == "hamming":
        window = torch.hamming_window(size, device=device)
    elif window_type == "blackman":
        window = torch.blackman_window(size, device=device)
    else:
        raise ValueError(f"Unknown window type: {window_type}")
    
    return window


def fft_magnitude_phase(tensor: Tensor) -> <PERSON><PERSON>[Tensor, Tensor]:
    """Compute magnitude and phase of FFT.
    
    Args:
        tensor: Input tensor
        
    Returns:
        Tuple of (magnitude, phase) tensors
    """
    fft_result = torch.fft.fft(tensor, dim=-1)
    magnitude = torch.abs(fft_result)
    phase = torch.angle(fft_result)
    return magnitude, phase


def apply_frequency_mask(
    tensor: Tensor,
    mask: Tensor,
    mode: str = "multiply"
) -> Tensor:
    """Apply a mask in frequency domain.
    
    Args:
        tensor: Input tensor
        mask: Frequency mask
        mode: How to apply mask ("multiply", "add", "gate")
        
    Returns:
        Masked tensor
    """
    tensor_fft = torch.fft.fft(tensor, dim=-1)
    
    if mode == "multiply":
        masked_fft = tensor_fft * mask
    elif mode == "add":
        masked_fft = tensor_fft + mask
    elif mode == "gate":
        # Sigmoid gating in frequency domain
        gate = torch.sigmoid(mask)
        masked_fft = tensor_fft * gate
    else:
        raise ValueError(f"Unknown mask mode: {mode}")
    
    return torch.fft.ifft(masked_fft, dim=-1).real


def frequency_domain_attention(
    query: Tensor,
    key: Tensor,
    value: Tensor,
    temperature: float = 1.0,
    use_frequency_filter: bool = False
) -> Tuple[Tensor, Tensor]:
    """Perform attention-like operation in frequency domain.
    
    This is an experimental operation that performs attention-like
    weighting in the frequency domain.
    
    Args:
        query: Query tensor [batch, seq_len, d_model]
        key: Key tensor [batch, seq_len, d_model]
        value: Value tensor [batch, seq_len, d_model]
        temperature: Softmax temperature
        use_frequency_filter: Whether to apply frequency domain filtering
        
    Returns:
        Tuple of (output, weights)
    """
    batch_size, seq_len, d_model = query.shape
    
    # Compute attention scores in input space (not frequency domain for weights)
    scores = torch.matmul(query, key.transpose(-2, -1)) / (d_model ** 0.5) / temperature
    weights = torch.softmax(scores, dim=-1)
    
    if use_frequency_filter:
        # Transform value to frequency domain for processing
        value_fft = torch.fft.fft(value, dim=-1)
        
        # Apply attention weights to frequency domain values
        # weights: [batch, seq_len, seq_len], value_fft: [batch, seq_len, d_model]
        # Convert weights to complex for matrix multiplication
        weights_complex = weights.to(dtype=value_fft.dtype)
        output_fft = torch.matmul(weights_complex, value_fft)
        
        # Transform back
        output = torch.fft.ifft(output_fft, dim=-1).real
    else:
        # Standard attention without frequency domain processing
        output = torch.matmul(weights, value)
    
    return output, weights


def spectral_regularization(tensor: Tensor, lambda_reg: float = 0.01) -> Tensor:
    """Apply spectral regularization to encourage smoothness.
    
    Args:
        tensor: Input tensor
        lambda_reg: Regularization strength
        
    Returns:
        Regularization loss
    """
    # Compute frequency magnitudes
    fft_result = torch.fft.fft(tensor, dim=-1)
    magnitudes = torch.abs(fft_result)
    
    # Penalize high frequencies (encourage smoothness)
    freq_weights = torch.linspace(0, 1, magnitudes.size(-1), device=tensor.device)
    weighted_mags = magnitudes * freq_weights
    
    return lambda_reg * weighted_mags.mean()


def adaptive_frequency_filtering(
    tensor: Tensor,
    context: Optional[Tensor] = None,
    n_filters: int = 8,
    intensity: float = 1.0
) -> Tensor:
    """Apply adaptive frequency filtering based on context.
    
    Args:
        tensor: Input tensor to filter
        context: Context tensor for computing filter parameters (defaults to tensor itself)
        n_filters: Number of learnable filters
        intensity: Filtering intensity (0.0 = no filtering, 1.0 = full filtering)
        
    Returns:
        Filtered tensor
    """
    batch_size, d_model = tensor.shape
    
    # Use tensor as context if not provided
    if context is None:
        context = tensor
    
    # Generate filter parameters from context
    # This would typically involve learnable projections
    filter_params = context.view(batch_size, n_filters, d_model // n_filters)
    
    # Apply filters in frequency domain
    tensor_fft = torch.fft.fft(tensor, dim=-1)
    
    # Reshape for grouped filtering
    tensor_fft_grouped = tensor_fft.view(batch_size, n_filters, -1)
    
    # Apply adaptive filtering per group with intensity control
    filter_weights = torch.sigmoid(filter_params)
    # Interpolate between original (intensity=0) and filtered (intensity=1)
    filtered_fft = tensor_fft_grouped * (1 - intensity + intensity * filter_weights)
    
    # Reshape back and inverse transform
    filtered_fft = filtered_fft.view(batch_size, d_model)
    return torch.fft.ifft(filtered_fft, dim=-1).real


def ensure_numerical_stability(tensor: Tensor, min_value: float = EPSILON) -> Tensor:
    """Ensure numerical stability by clamping small values and handling NaN/Inf.
    
    Args:
        tensor: Input tensor
        min_value: Minimum absolute value
        
    Returns:
        Stabilized tensor
    """
    # First handle NaN and infinity values
    tensor = torch.where(torch.isfinite(tensor), tensor, torch.zeros_like(tensor))
    
    # Then clamp small values
    return torch.where(
        torch.abs(tensor) < min_value,
        torch.sign(tensor) * min_value,
        tensor
    )