# Git ignore rules for DELTA project

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# Jupyter Notebook
.ipynb_checkpoints

# PyTorch
*.pth
*.pt
*.ckpt

# Tensorboard
runs/
logs/
tensorboard_logs/

# Weights & Biases
wandb/

# Data
data/raw/
data/processed/
data/datasets/
*.csv
*.json
*.jsonl
*.parquet
*.h5
*.hdf5

# Model checkpoints
checkpoints/
models/
saved_models/
outputs/
results/

# Configuration overrides
configs/local/
*.local.yaml
*.local.yml

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp
*.log

# Docker
.dockerignore

# Coverage
.coverage
htmlcov/

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# pytest
.pytest_cache/

# Documentation
docs/_build/
docs/build/
