"""
pytest configuration for DELTA mathematical tests.

This file contains pytest configuration and fixtures for running
comprehensive mathematical tests for the DELTA architecture.
"""

import pytest
import torch
import numpy as np
import sys
import os
from typing import Generator, Dict, Any


# Add source directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..', 'source'))


@pytest.fixture(scope="session")
def torch_device():
    """Provide the appropriate torch device for testing."""
    if torch.cuda.is_available():
        return torch.device("cuda")
    elif hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
        return torch.device("mps")
    else:
        return torch.device("cpu")


@pytest.fixture(scope="session") 
def test_config():
    """Provide test configuration parameters."""
    return {
        'batch_size': 4,
        'd_model': 64,
        'seq_len': 8,
        'num_heads': 8,
        'rtol': 1e-5,
        'atol': 1e-6,
        'seed': 42
    }


@pytest.fixture(autouse=True)
def set_random_seeds():
    """Set random seeds for reproducible tests."""
    torch.manual_seed(42)
    np.random.seed(42)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(42)


@pytest.fixture
def sample_tensors(test_config, torch_device):
    """Provide sample tensors for testing."""
    batch_size = test_config['batch_size']
    d_model = test_config['d_model']
    seq_len = test_config['seq_len']
    
    return {
        'V_T': torch.randn(batch_size, d_model, device=torch_device),
        'kappa': torch.randn(batch_size, d_model, device=torch_device),
        'K_i': torch.randn(batch_size, seq_len, d_model, device=torch_device),
        'query': torch.randn(batch_size, seq_len, d_model, device=torch_device),
        'key': torch.randn(batch_size, seq_len, d_model, device=torch_device),
        'value': torch.randn(batch_size, seq_len, d_model, device=torch_device),
        'input_ids': torch.randint(0, 1000, (batch_size, seq_len), device=torch_device),
        'attention_mask': torch.ones(batch_size, seq_len, device=torch_device)
    }


@pytest.fixture
def complex_tensors(test_config, torch_device):
    """Provide complex tensors for FFT testing."""
    batch_size = test_config['batch_size']
    d_model = test_config['d_model']
    
    real_part = torch.randn(batch_size, d_model, device=torch_device)
    imag_part = torch.randn(batch_size, d_model, device=torch_device)
    
    return {
        'complex_input': torch.complex(real_part, imag_part),
        'real_input': real_part,
        'phase': torch.randn(batch_size, d_model, device=torch_device) * 2 * np.pi
    }


@pytest.fixture
def gradient_test_tensors(test_config, torch_device):
    """Provide tensors with gradients enabled for gradient testing."""
    batch_size = test_config['batch_size']
    d_model = test_config['d_model']
    
    return {
        'thesis': torch.randn(batch_size, d_model, requires_grad=True, device=torch_device),
        'antithesis': torch.randn(batch_size, d_model, requires_grad=True, device=torch_device),
        'synthesis': torch.randn(batch_size, d_model, requires_grad=True, device=torch_device),
        'context': torch.randn(batch_size, d_model, requires_grad=True, device=torch_device)
    }


@pytest.fixture
def attention_test_data(test_config, torch_device):
    """Provide test data specifically for attention mechanisms."""
    batch_size = test_config['batch_size']
    seq_len = test_config['seq_len']
    d_model = test_config['d_model']
    num_heads = test_config['num_heads']
    
    return {
        'batch_size': batch_size,
        'seq_len': seq_len,
        'd_model': d_model,
        'num_heads': num_heads,
        'd_head': d_model // num_heads,
        'queries': torch.randn(batch_size, seq_len, d_model, device=torch_device),
        'keys': torch.randn(batch_size, seq_len, d_model, device=torch_device),
        'values': torch.randn(batch_size, seq_len, d_model, device=torch_device),
        'kappa_cache': torch.randn(batch_size, seq_len, d_model, device=torch_device),
        'thesis_new': torch.randn(batch_size, d_model, device=torch_device),
        'causal_mask': torch.tril(torch.ones(seq_len, seq_len, device=torch_device)),
        'padding_mask': torch.ones(batch_size, seq_len, device=torch_device)
    }


@pytest.fixture
def edge_case_inputs(torch_device):
    """Provide edge case inputs for robust testing."""
    return {
        'zeros': torch.zeros(4, 64, device=torch_device),
        'ones': torch.ones(4, 64, device=torch_device),
        'large_values': torch.ones(4, 64, device=torch_device) * 1e6,
        'small_values': torch.ones(4, 64, device=torch_device) * 1e-6,
        'negative_values': torch.ones(4, 64, device=torch_device) * -1.0,
        'mixed_signs': torch.tensor([1.0, -1.0, 1.0, -1.0] * 16, device=torch_device).reshape(4, 64),
        'inf_values': torch.tensor([float('inf'), float('-inf'), 1.0, -1.0] * 16, device=torch_device).reshape(4, 64),
        'nan_values': torch.tensor([float('nan'), 1.0, -1.0, 0.0] * 16, device=torch_device).reshape(4, 64)
    }


def pytest_configure(config):
    """Configure pytest for DELTA mathematical tests."""
    config.addinivalue_line(
        "markers", "slow: marks tests as slow (deselect with '-m \"not slow\"')"
    )
    config.addinivalue_line(
        "markers", "gpu: marks tests that require GPU (deselect with '-m \"not gpu\"')"
    )
    config.addinivalue_line(
        "markers", "fft: marks tests for FFT operations"
    )
    config.addinivalue_line(
        "markers", "attention: marks tests for attention mechanisms"
    )
    config.addinivalue_line(
        "markers", "activation: marks tests for activation functions"
    )
    config.addinivalue_line(
        "markers", "normalization: marks tests for normalization operations"
    )
    config.addinivalue_line(
        "markers", "tensor_ops: marks tests for tensor operations"
    )
    config.addinivalue_line(
        "markers", "advanced: marks tests for advanced mathematical operations"
    )
    config.addinivalue_line(
        "markers", "gradient: marks tests that require gradient computation"
    )
    config.addinivalue_line(
        "markers", "numerical_stability: marks tests for numerical stability"
    )
    config.addinivalue_line(
        "markers", "specification: marks tests that verify specification compliance"
    )


def pytest_collection_modifyitems(config, items):
    """Modify test collection to add appropriate markers."""
    for item in items:
        # Add markers based on test file names
        if "fft" in item.nodeid:
            item.add_marker(pytest.mark.fft)
        if "attention" in item.nodeid:
            item.add_marker(pytest.mark.attention)
        if "activation" in item.nodeid:
            item.add_marker(pytest.mark.activation)
        if "normalization" in item.nodeid:
            item.add_marker(pytest.mark.normalization)
        if "tensor" in item.nodeid:
            item.add_marker(pytest.mark.tensor_ops)
        if "advanced" in item.nodeid:
            item.add_marker(pytest.mark.advanced)
        
        # Add markers based on test names
        if "gradient" in item.name.lower():
            item.add_marker(pytest.mark.gradient)
        if "stability" in item.name.lower():
            item.add_marker(pytest.mark.numerical_stability)
        if "spec" in item.name.lower() or "compliance" in item.name.lower():
            item.add_marker(pytest.mark.specification)
        if "gpu" in item.name.lower() or "cuda" in item.name.lower():
            item.add_marker(pytest.mark.gpu)


@pytest.fixture
def assert_delta_property():
    """Fixture providing assertion helpers for DELTA-specific properties."""
    
    def _assert_synthesis_equation(V_T, kappa, V_S_raw):
        """Assert that synthesis follows: V_S,raw = real(iFFT(FFT(V_T) ⊙ FFT(κ)))"""
        # Convert to complex for FFT operations
        V_T_complex = V_T.to(torch.complex64)
        kappa_complex = kappa.to(torch.complex64)
        
        # Compute expected result
        V_T_fft = torch.fft.fft(V_T_complex, dim=-1)
        kappa_fft = torch.fft.fft(kappa_complex, dim=-1)
        synthesis_fft = V_T_fft * kappa_fft
        expected = torch.fft.ifft(synthesis_fft, dim=-1).real
        
        torch.testing.assert_close(V_S_raw, expected, rtol=1e-5, atol=1e-6)
    
    def _assert_attention_properties(attention_weights):
        """Assert attention weights satisfy probability properties."""
        # Should sum to 1 along last dimension
        sums = attention_weights.sum(dim=-1)
        torch.testing.assert_close(sums, torch.ones_like(sums), rtol=1e-5, atol=1e-6)
        
        # Should be non-negative
        assert (attention_weights >= 0).all(), "Attention weights must be non-negative"
    
    def _assert_gradient_flow(tensors_with_grad):
        """Assert that gradients flow correctly through operations."""
        for name, tensor in tensors_with_grad.items():
            if tensor.requires_grad:
                assert tensor.grad is not None, f"Gradient not computed for {name}"
                assert torch.isfinite(tensor.grad).all(), f"Non-finite gradients in {name}"
    
    def _assert_numerical_stability(output):
        """Assert numerical stability of operations."""
        assert torch.isfinite(output).all(), "Output contains non-finite values"
        assert not torch.isnan(output).any(), "Output contains NaN values"
        assert not torch.isinf(output).any(), "Output contains infinite values"
    
    return {
        'synthesis_equation': _assert_synthesis_equation,
        'attention_properties': _assert_attention_properties,
        'gradient_flow': _assert_gradient_flow,
        'numerical_stability': _assert_numerical_stability
    }


# Custom assertions for better error messages
def assert_tensor_properties(tensor, expected_shape=None, dtype=None, device=None, finite=True):
    """Assert multiple tensor properties with clear error messages."""
    if expected_shape is not None:
        assert tensor.shape == expected_shape, f"Shape mismatch: got {tensor.shape}, expected {expected_shape}"
    
    if dtype is not None:
        assert tensor.dtype == dtype, f"Dtype mismatch: got {tensor.dtype}, expected {dtype}"
    
    if device is not None:
        assert tensor.device == device, f"Device mismatch: got {tensor.device}, expected {device}"
    
    if finite:
        assert torch.isfinite(tensor).all(), "Tensor contains non-finite values"


def assert_delta_equation_compliance(operation_name, inputs, outputs, expected_equation):
    """Assert that an operation follows the expected DELTA equation."""
    print(f"Verifying {operation_name} follows equation: {expected_equation}")
    # This would be expanded with specific equation checking logic
    pass


# Performance measurement helpers
@pytest.fixture
def measure_performance():
    """Fixture for measuring test performance."""
    import time
    
    def _measure(func, *args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        
        return result, end_time - start_time
    
    return _measure
