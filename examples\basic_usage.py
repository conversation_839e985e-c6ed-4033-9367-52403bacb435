"""Basic usage example for DELTA decoder.

This script demonstrates how to:
1. Initialize a DELTA model
2. Run the three-phase cycle (Prefill, Decode, Mutate)
3. Generate text with the model
"""

import torch
from source.models import DELTAModel, DELTAConfig
from source.training.dual_objective import create_dual_objective_trainer


def main():
    # Set device
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")
    
    # Create model configuration
    config = DELTAConfig(
        d_model=512,
        n_layers=12,
        n_attractors=8,
        semantic_hidden_size=256,
        vocab_size=50257,
        max_seq_length=2048,
        dropout=0.1
    )
    
    # Initialize model
    model = DELTAModel(config).to(device)
    print(f"Model initialized with {sum(p.numel() for p in model.parameters())} parameters")
    
    # Example 1: Basic generation
    print("\n=== Example 1: Basic Generation ===")
    
    # Create dummy prompt (normally you'd use a tokenizer)
    prompt_ids = torch.randint(0, config.vocab_size, (1, 10)).to(device)
    print(f"Prompt shape: {prompt_ids.shape}")
    
    # Generate text
    generated_ids, details = model.generate(
        prompt_ids,
        max_length=50,
        temperature=0.8,
        do_mutation=False,
        return_details=True
    )
    print(f"Generated shape: {generated_ids.shape}")
    
    # Example 2: Three-phase cycle with mutation
    print("\n=== Example 2: Three-Phase Cycle ===")
    
    # Phase 1: Prefill
    prefill_outputs = model(prompt_ids, phase="prefill")
    kappa_cache = prefill_outputs["kappa_cache"]
    print(f"κ-cache initialized with shape: {kappa_cache.cache[0].shape}")
    
    # Phase 2: Decode (single step)
    next_token = torch.randint(0, config.vocab_size, (1, 1)).to(device)
    decode_outputs = model(next_token, phase="decode", kappa_cache=kappa_cache)
    logits = decode_outputs["logits"]
    print(f"Decode logits shape: {logits.shape}")
    
    # Phase 3: Mutate (with dummy synthesis states)
    synthesis_states = [decode_outputs["h_out"] for _ in range(5)]
    mutation_info = model.mutate(synthesis_states)
    print(f"Mutation applied: {mutation_info['mutation_applied']}")
    
    # Example 3: Training with dual-objective
    print("\n=== Example 3: Dual-Objective Training Setup ===")
    
    # Create trainer
    trainer = create_dual_objective_trainer(
        model,
        thesis_lr=1e-3,
        antithesis_lr=1e-3
    )
    print("Dual-objective trainer created")
    
    # Dummy training step
    input_ids = torch.randint(0, config.vocab_size, (4, 20)).to(device)
    target_ids = torch.randint(0, config.vocab_size, (4, 20)).to(device)
    
    # Note: This would normally be in a training loop
    # losses = trainer.train_step(input_ids, target_ids)
    # print(f"Training losses: {losses}")
    
    print("\n=== DELTA Model Successfully Initialized! ===")


if __name__ == "__main__":
    main()
