"""Custom exception classes for DELTA implementation.

This module provides specific exception classes to improve error handling
and debugging throughout the DELTA codebase.
"""


class DELTAError(Exception):
    """Base exception for DELTA-related errors."""
    pass


class CacheNotPopulatedError(DELTAError):
    """Raised when trying to use an unpopulated κ-Cache."""
    
    def __init__(self, message="κ-Cache is not properly populated"):
        super().__init__(message)


class InvalidPhaseError(DELTAError):
    """Raised when an invalid phase operation is attempted."""
    
    def __init__(self, current_phase, attempted_operation):
        message = f"Cannot perform '{attempted_operation}' in {current_phase} phase"
        super().__init__(message)


class GradientSeparationError(DELTAError):
    """Raised when gradient separation fails in dual-objective learning."""
    
    def __init__(self, message="Failed to maintain gradient separation"):
        super().__init__(message)


class NumericalInstabilityError(DELTAError):
    """Raised when numerical instability is detected."""
    
    def __init__(self, component, details=""):
        message = f"Numerical instability detected in {component}"
        if details:
            message += f": {details}"
        super().__init__(message)


class ConfigurationError(DELTAError):
    """Raised when configuration is invalid or inconsistent."""
    
    def __init__(self, message="Invalid configuration"):
        super().__init__(message)


class AttractorStateError(DELTAError):
    """Raised when attractor states are in an invalid condition."""
    
    def __init__(self, message="Invalid attractor state"):
        super().__init__(message)
