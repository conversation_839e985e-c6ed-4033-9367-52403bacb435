"""Global Loss Functions for Dual-Objective Learning

This module implements global loss functions that are applied to antithesis/control
components in the dual-objective learning scheme. The global loss guides the entire
system toward correct task completion.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch import Tensor
from typing import Optional, Union

from ..math.advanced.numerical_stability import stable_cross_entropy


def compute_global_task_loss(
    model_output: Tensor,
    targets: Tensor,
    loss_type: str = "cross_entropy",
    label_smoothing: float = 0.0,
    ignore_index: int = -100,
    reduction: str = "mean"
) -> Tensor:
    """
    Compute the global task loss for language modeling.
    
    This is the standard cross-entropy loss computed on the final output that
    guides the entire system toward the correct answer. It's applied to
    antithesis/control components in dual-objective learning.
    
    Args:
        model_output: Final model output states (batch_size, d_model) or
                     logits (batch_size, vocab_size)
        targets: Target token IDs (batch_size,) or (batch_size, seq_len)
        loss_type: Type of loss function ("cross_entropy", "mse", "focal")
        label_smoothing: Label smoothing factor for cross-entropy loss
        ignore_index: Index to ignore in loss computation
        reduction: Reduction method ("mean", "sum", "none")
        
    Returns:
        Global task loss tensor
    """
    if loss_type == "cross_entropy":
        return _compute_cross_entropy_loss(
            model_output, targets, label_smoothing, ignore_index, reduction
        )
    elif loss_type == "mse":
        return _compute_mse_loss(model_output, targets, reduction)
    elif loss_type == "focal":
        return _compute_focal_loss(model_output, targets, ignore_index, reduction)
    else:
        raise ValueError(f"Unsupported loss type: {loss_type}")


def _compute_cross_entropy_loss(
    logits: Tensor,
    targets: Tensor,
    label_smoothing: float = 0.0,
    ignore_index: int = -100,
    reduction: str = "mean"
) -> Tensor:
    """Compute cross-entropy loss with optional label smoothing."""
    
    # Handle different input shapes
    if logits.dim() == 2 and targets.dim() == 1:
        # Standard case: (batch_size, vocab_size) and (batch_size,)
        if label_smoothing > 0.0:
            return F.cross_entropy(
                logits, targets, 
                label_smoothing=label_smoothing,
                ignore_index=ignore_index,
                reduction=reduction
            )
        else:
            # Use stable cross-entropy for numerical stability
            return stable_cross_entropy(logits, targets, reduction=reduction)
            
    elif logits.dim() == 3 and targets.dim() == 2:
        # Sequence case: (batch_size, seq_len, vocab_size) and (batch_size, seq_len)
        batch_size, seq_len, vocab_size = logits.shape
        
        # Reshape for standard cross-entropy
        logits_flat = logits.view(-1, vocab_size)
        targets_flat = targets.view(-1)
        
        if label_smoothing > 0.0:
            loss = F.cross_entropy(
                logits_flat, targets_flat,
                label_smoothing=label_smoothing,
                ignore_index=ignore_index,
                reduction=reduction
            )
        else:
            loss = stable_cross_entropy(logits_flat, targets_flat, reduction=reduction)
            
        return loss
        
    else:
        raise ValueError(f"Incompatible shapes: logits {logits.shape}, targets {targets.shape}")


def _compute_mse_loss(
    predictions: Tensor,
    targets: Tensor,
    reduction: str = "mean"
) -> Tensor:
    """Compute MSE loss for regression tasks."""
    return F.mse_loss(predictions, targets.float(), reduction=reduction)


def _compute_focal_loss(
    logits: Tensor,
    targets: Tensor,
    alpha: float = 1.0,
    gamma: float = 2.0,
    ignore_index: int = -100,
    reduction: str = "mean"
) -> Tensor:
    """
    Compute focal loss for handling class imbalance.
    
    Focal Loss: FL(p_t) = -α(1-p_t)^γ log(p_t)
    
    Args:
        logits: Predicted logits
        targets: Target class indices
        alpha: Weighting factor for rare class
        gamma: Focusing parameter
        ignore_index: Index to ignore
        reduction: Reduction method
        
    Returns:
        Focal loss tensor
    """
    ce_loss = F.cross_entropy(logits, targets, ignore_index=ignore_index, reduction='none')
    pt = torch.exp(-ce_loss)
    focal_loss = alpha * (1 - pt) ** gamma * ce_loss
    
    if reduction == "mean":
        return focal_loss.mean()
    elif reduction == "sum":
        return focal_loss.sum()
    else:
        return focal_loss


def compute_perplexity(
    logits: Tensor,
    targets: Tensor,
    ignore_index: int = -100
) -> Tensor:
    """
    Compute perplexity metric from logits and targets.
    
    Args:
        logits: Model logits (batch_size, vocab_size) or (batch_size, seq_len, vocab_size)
        targets: Target tokens (batch_size,) or (batch_size, seq_len)
        ignore_index: Index to ignore in computation
        
    Returns:
        Perplexity value
    """
    loss = _compute_cross_entropy_loss(
        logits, targets, 
        ignore_index=ignore_index, 
        reduction="mean"
    )
    return torch.exp(loss)


def compute_accuracy(
    logits: Tensor,
    targets: Tensor,
    ignore_index: int = -100,
    top_k: int = 1
) -> Tensor:
    """
    Compute accuracy metric from logits and targets.
    
    Args:
        logits: Model logits (batch_size, vocab_size) or (batch_size, seq_len, vocab_size)
        targets: Target tokens (batch_size,) or (batch_size, seq_len)
        ignore_index: Index to ignore in computation
        top_k: Compute top-k accuracy
        
    Returns:
        Accuracy as a fraction between 0 and 1
    """
    # Get predictions
    if top_k == 1:
        predictions = torch.argmax(logits, dim=-1)
        correct = (predictions == targets)
    else:
        _, top_k_preds = torch.topk(logits, top_k, dim=-1)
        targets_expanded = targets.unsqueeze(-1).expand_as(top_k_preds)
        correct = torch.any(top_k_preds == targets_expanded, dim=-1)
    
    # Mask out ignore_index
    if ignore_index is not None:
        mask = (targets != ignore_index)
        correct = correct & mask
        total = mask.sum().float()
    else:
        total = targets.numel()
    
    if total == 0:
        return torch.tensor(0.0, device=logits.device)
    
    return correct.sum().float() / total


class AdaptiveGlobalLoss(nn.Module):
    """
    Adaptive global loss that can switch between different loss functions
    based on training progress or performance metrics.
    """
    
    def __init__(
        self,
        primary_loss: str = "cross_entropy",
        secondary_loss: Optional[str] = None,
        adaptation_threshold: float = 0.1,
        label_smoothing: float = 0.0
    ):
        super().__init__()
        self.primary_loss = primary_loss
        self.secondary_loss = secondary_loss
        self.adaptation_threshold = adaptation_threshold
        self.label_smoothing = label_smoothing
        
        # Track loss history for adaptation
        self.loss_history = []
        self.current_loss_type = primary_loss
        
    def forward(
        self,
        model_output: Tensor,
        targets: Tensor,
        **kwargs
    ) -> Tensor:
        """Compute adaptive global loss."""
        
        # Compute primary loss
        primary_loss_val = compute_global_task_loss(
            model_output, targets, 
            loss_type=self.primary_loss,
            label_smoothing=self.label_smoothing,
            **kwargs
        )
        
        # Check if we should adapt to secondary loss
        if self.secondary_loss and self._should_adapt():
            secondary_loss_val = compute_global_task_loss(
                model_output, targets,
                loss_type=self.secondary_loss,
                **kwargs
            )
            
            # Weighted combination based on recent performance
            weight = self._compute_adaptation_weight()
            final_loss = (1 - weight) * primary_loss_val + weight * secondary_loss_val
        else:
            final_loss = primary_loss_val
        
        # Update loss history
        self.loss_history.append(final_loss.item())
        if len(self.loss_history) > 100:  # Keep only recent history
            self.loss_history.pop(0)
            
        return final_loss
    
    def _should_adapt(self) -> bool:
        """Determine if loss adaptation should occur."""
        if len(self.loss_history) < 10:
            return False
            
        # Check if loss has plateaued
        recent_losses = self.loss_history[-10:]
        loss_variance = torch.var(torch.tensor(recent_losses)).item()
        
        return loss_variance < self.adaptation_threshold
    
    def _compute_adaptation_weight(self) -> float:
        """Compute weight for secondary loss based on training progress."""
        if len(self.loss_history) < 20:
            return 0.0
            
        # Gradually increase secondary loss weight if primary loss plateaus
        recent_variance = torch.var(torch.tensor(self.loss_history[-10:])).item()
        return min(0.5, 0.1 / (recent_variance + 1e-8))
