"""Prefill Phase Implementation

This module implements the Prefill Phase as described in Section 4.1 of the DELTA specification.
The Prefill Phase processes the input prompt sequence token-by-token to generate and populate 
the κ-Cache with constraint vectors.
"""

import torch
import torch.nn as nn
from typing import List, Optional, Tuple, Dict
import logging

from ..core.base.types import DELT<PERSON>onfig, KappaCache, PhaseOutput
from ..core.base.abstract_implementations import AbstractPrefillPhase
from ..core.delta_stack import DELTAStack, DELTAStackOutput
from ..math.advanced.numerical_stability import ensure_numerical_stability

logger = logging.getLogger(__name__)


class PrefillPhase(AbstractPrefillPhase):
    """
    Implements the Prefill Phase (§4.1) - "The Plan"
    
    This phase runs once over the input prompt sequence P = (x_1, ..., x_L) to generate
    the κ-Cache. The prompt sequence is processed token-by-token to populate the κ-Cache
    with constraint vectors that will be used during the subsequent Decoding Phase.
    
    Key characteristics:
    - Sequential processing across sequence length L (O(L) complexity)
    - Vertical parallelizable processing through N DELTA blocks
    - Populates κ-Cache with layer-specific constraint vectors
    - Updates recurrent Semantic Attractor states sequentially
    """
    
    def __init__(self, config: DELTAConfig, delta_stack: DELTAStack):
        """Initialize Prefill Phase.
        
        Args:
            config: DELTA model configuration
            delta_stack: The stack of DELTA blocks to process through
        """
        self.config = config
        self.delta_stack = delta_stack
        self.logger = logger
        
    def execute(
        self,
        prompt_embeddings: torch.Tensor,
        initial_semantic_states: Optional[List[List[torch.Tensor]]] = None
    ) -> PhaseOutput:
        """
        Execute the Prefill Phase over the prompt sequence.
        
        Implements Algorithm from §4.1:
        1. Initial State Formulation: h_in = x_j + p_j  
        2. Sequential Processing: Process tokens j=1..L sequentially
        3. κ-Cache Generation: Store κ_{j,i} for each token j and layer i
        4. State Updates: Update semantic attractor states sequentially
        
        Args:
            prompt_embeddings: Token embeddings + positional encodings
                              Shape: (batch_size, seq_length, d_model)
            initial_semantic_states: Optional initial semantic attractor states
                                   List of length n_layers, each containing List[attractor_states]
                                   
        Returns:
            PhaseOutput containing:
            - final_state: h_{out,L,N} (final state for last token, last layer)
            - kappa_cache: Populated κ-Cache for use in Decoding Phase
            - semantic_states: Updated semantic states for all layers
        """
        batch_size, seq_length, d_model = prompt_embeddings.shape
        
        # Validate configuration
        if d_model != self.config.d_model:
            raise ValueError(f"Expected d_model={self.config.d_model}, got {d_model}")
            
        # Initialize κ-Cache (Algorithm §4.1, Step 2)
        kappa_cache = KappaCache(
            num_layers=self.config.n_layers,
            d_model=self.config.d_model,
            max_seq_length=self.config.max_seq_length,
            dtype=prompt_embeddings.dtype
        )
        kappa_cache.initialize(batch_size, seq_length, prompt_embeddings.device)
        
        # Initialize semantic states if not provided
        if initial_semantic_states is None:
            initial_semantic_states = self._initialize_semantic_states(batch_size, prompt_embeddings.device)
            
        # Sequential processing across the prompt sequence (Algorithm §4.1, Step 2)
        self.logger.info(f"Starting prefill for sequence length {seq_length}")
        
        current_semantic_states = initial_semantic_states
        final_state = None
        
        for token_pos in range(seq_length):
            # Get current token state: h_in = x_j + p_j (Algorithm §4.1, Step 1)
            token_state = prompt_embeddings[:, token_pos, :]  # (batch_size, d_model)
            
            # Apply numerical stability
            token_state = ensure_numerical_stability(token_state, f"prefill_token_{token_pos}")
            
            # Convert semantic states to Optional format for DELTAStack
            optional_semantic_states = [states if states is not None else None for states in current_semantic_states]
            
            # Process through DELTA stack (prefill mode - no cache attention)
            stack_output = self.delta_stack.forward(
                h_in=token_state,
                h_sem_in=optional_semantic_states,
                kappa_cache=None  # No cache attention during prefill
            )
            
            # Populate κ-Cache with local antitheses from this forward pass
            # Per §4.1: "κ_{j,i} is stored in the κ-Cache"
            for layer_idx, local_antithesis in enumerate(stack_output.all_local_antitheses):
                kappa_cache.set_vector_at_position(
                    layer_idx=layer_idx,
                    position=token_pos,
                    kappa_vector=local_antithesis
                )
            
            # Update states for next token - convert from Optional format
            current_semantic_states = self._convert_optional_states_to_list(stack_output.h_sem_out)
            final_state = stack_output.h_out
            
            # Log progress for long sequences
            if token_pos > 0 and token_pos % 100 == 0:
                self.logger.debug(f"Prefill progress: {token_pos}/{seq_length} tokens processed")
        
        self.logger.info(f"Prefill completed. κ-Cache populated with {seq_length} positions across {self.config.n_layers} layers")
        
        return PhaseOutput(
            final_state=final_state,
            kappa_cache=kappa_cache,
            semantic_states=current_semantic_states
        )
    
    def _initialize_semantic_states(
        self, 
        batch_size: int, 
        device: torch.device
    ) -> List[List[torch.Tensor]]:
        """Initialize semantic attractor states for all layers.
        
        Args:
            batch_size: Batch size for state tensors
            device: Device to create states on
            
        Returns:
            List of length n_layers, each containing list of attractor states
        """
        states_list = []
        
        for layer_idx in range(self.config.n_layers):
            # Get fresh states from the layer's semantic attractors
            layer_states = self.delta_stack.layers[layer_idx].semantic_attractors.reset_state(batch_size)
            states_list.append(layer_states)
            
        return states_list
    
    def _convert_optional_states_to_list(
        self, 
        optional_states: List[Optional[List[torch.Tensor]]]
    ) -> List[List[torch.Tensor]]:
        """Convert from DELTAStack Optional format to standardized list format.
        
        Args:
            optional_states: States from DELTAStack with Optional wrapper
            
        Returns:
            Standardized list format, replacing None with empty lists
        """
        result = []
        for states in optional_states:
            if states is not None:
                result.append(states)
            else:
                # Initialize empty states for this layer if None
                result.append([])
        return result
    
    def validate_cache_population(self, kappa_cache: KappaCache) -> bool:
        """Validate that the κ-Cache was properly populated during prefill.
        
        Args:
            kappa_cache: The populated cache to validate
            
        Returns:
            True if cache is valid, False otherwise
        """
        if not kappa_cache.is_populated():
            self.logger.error("κ-Cache is not populated")
            return False
            
        # Check that all layers have entries
        for layer_idx in range(self.config.n_layers):
            try:
                layer_cache = kappa_cache.get_cache_for_layer(layer_idx)
                if layer_cache is None:
                    self.logger.error(f"Layer {layer_idx} cache is None")
                    return False
                    
                # Check for NaN or Inf values
                if not torch.isfinite(layer_cache).all():
                    self.logger.error(f"Layer {layer_idx} cache contains invalid values")
                    return False
                    
            except Exception as e:
                self.logger.error(f"Error accessing layer {layer_idx} cache: {e}")
                return False
                
        self.logger.info("κ-Cache validation passed")
        return True