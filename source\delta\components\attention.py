import torch
import torch.nn as nn
import torch.nn.functional as F

class KappaCacheAttention(nn.Module):
    """
    Implements the κ-Cache attention and fusion mechanism described in Section 4.2
    of the DELTA specification.

    This module is used during the Decoding Phase. It takes the new Thesis vector,
    queries the layer-specific κ-Cache from the Prefill Phase, and fuses the
    retrieved context with the newly computed Antithesis vector.

    Args:
        d_model (int): The dimensionality of the model.
    """
    def __init__(self, d_model: int):
        super().__init__()
        self.d_model = d_model
        self.scale = d_model ** -0.5

        # Linear layer for fusing κ_new and κ_context, as per the spec:
        # κ_effective = W_κ[κ_new; κ_context] + b_κ
        self.fusion_projection = nn.Linear(d_model * 2, d_model, bias=True)
        
        # Explicit Xavier uniform initialization for reproducibility (consistent with other projections)
        nn.init.xavier_uniform_(self.fusion_projection.weight, gain=1.0)
        nn.init.zeros_(self.fusion_projection.bias)

    def forward(self,
                v_thesis_new: torch.Tensor,
                kappa_new: torch.Tensor,
                kappa_cache: torch.Tensor) -> torch.Tensor:
        """
        Performs the forward pass for κ-Cache attention.

        Args:
            v_thesis_new (torch.Tensor): The newly generated Thesis vector for the current
                                         decoding step. Shape: (batch_size, d_model).
            kappa_new (torch.Tensor): The newly generated local Antithesis vector for
                                      the current decoding step. Shape: (batch_size, d_model).
            kappa_cache (torch.Tensor): The pre-computed κ-Cache for this layer from the
                                        Prefill Phase. Shape: (batch_size, prompt_len, d_model).

        Returns:
            torch.Tensor: The effective Antithesis vector, κ_effective.
                          Shape: (batch_size, d_model).
        """
        # The query is the new Thesis vector.
        # The keys and values are both the kappa_cache.
        # Unsqueeze v_thesis_new for matrix multiplication: (B, d_model) -> (B, d_model, 1)
        query = v_thesis_new.unsqueeze(-1)

        # 1. Compute attention scores (alpha) with proper scaling
        # (B, L, d) @ (B, d, 1) -> (B, L, 1)
        attention_scores = torch.bmm(kappa_cache, query)
        
        # Apply scaling: divide by √d_model as per equation 269 in spec
        scaled_scores = attention_scores * self.scale  # scale = d_model^{-0.5}
        
        # Clamp for numerical stability
        scaled_scores = torch.clamp(scaled_scores, min=-50, max=50)
        
        alpha = F.softmax(scaled_scores, dim=1)  # Softmax over prompt_len (L)

        # 2. Compute context vector (κ_context)
        # alpha^T @ K_i -> (B, 1, L) @ (B, L, d) -> (B, 1, d)
        kappa_context = torch.bmm(alpha.transpose(1, 2), kappa_cache)
        kappa_context = kappa_context.squeeze(1) # -> (B, d)

        # 3. Fuse κ_new and κ_context to form κ_effective
        # Concatenate along the feature dimension
        combined_kappa = torch.cat([kappa_new, kappa_context], dim=-1) # (B, 2*d)
        kappa_effective = self.fusion_projection(combined_kappa) # (B, d)

        return kappa_effective