"""
Implements the LowRankOperator<PERSON><PERSON><PERSON><PERSON>, a sophisticated expert for the
DELTA architecture that learns a transformation instead of a direction.
"""

import torch
from torch import Tensor

from ...core.base.abstract_implementations import AbstractAttractor
from ...components.operator import LowRankOperator


class LowRankOperatorAttractor(AbstractAttractor):
    """
    An expert attractor that learns a transformation (an operator)
    instead of just a direction. It uses a low-rank factorization
    to represent the operator efficiently.

    This is a stateless, structural-type attractor that applies its
    learned transformation to the Thesis vector to produce a force vector.

    Args:
        d_model (int): The dimensionality of the model.
        rank (int): The rank of the low-rank operator.
    """

    def __init__(self, d_model: int, rank: int, **kwargs):
        super().__init__(d_model, **kwargs)
        if not (rank > 0):
            raise ValueError("Rank must be a positive integer.")
        self.rank = rank
        self.operator = LowRankOperator(d_model, rank, bias=False)

    def forward(self, V_T: Tensor) -> Tensor:
        """Applies the learned low-rank operator to the Thesis vector."""
        transformed_v_t = self.operator(V_T)
        force_vector = torch.tanh(transformed_v_t)
        return force_vector

    def __repr__(self):
        return f"{self.__class__.__name__}(d_model={self.d_model}, " f"rank={self.rank})"

