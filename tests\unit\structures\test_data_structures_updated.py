"""
Test suite for data structures in DELTA architecture.

This module implements comprehensive tests for all tensor shapes, types,
and transformations mentioned in the DELTA specification, now using
the actual implemented modules.

Tests cover all tensor structures from the DeltaDecoder specification:
- Input tensors: X ∈ ℝ^(T×d)
- Hidden states: h ∈ ℝ^d
- Thesis representations: V_T ∈ ℝ^(T×d)
- Antithesis vectors: κ ∈ ℝ^d
- Synthesis outputs: V_S ∈ ℝ^(T×d)
- Gating vectors: g ∈ ℝ^k
- Attractor states: f_i ∈ ℝ^d
- κ-Cache structures and operations
- All tensor broadcasting, concatenation, and element-wise operations

Updated to test actual implementations from Phase 1.
"""

import pytest
import torch
import numpy as np
from typing import Tuple, Optional, List, Dict
import warnings


class TestDataStructures:
    """
    Test class for DELTA data structures.
    
    Tests all fundamental tensor operations and data structures
    specified in the DELTA architecture paper.
    """
    
    def setup_method(self):
        """Set up test fixtures before each test method."""
        # Set random seed for reproducibility
        torch.manual_seed(42)
        np.random.seed(42)
        
        # Define test dimensions based on DELTA spec
        self.batch_size = 4  # B
        self.d = 64          # model dimension d  
        self.T = 16          # sequence length T
        self.k = 8           # number of attractors k
        self.N = 6           # number of layers

        # Tolerance for numerical comparisons
        self.tolerance = 1e-6

    def test_input_tensor_structure(self):
        """
        Test X ∈ ℝ^(T×d) - sequence length T, embedding dimension d

        Tests the input tensor structure for DELTA processing.
        Now tests our actual implementation.
        """
        # Create test input tensor
        X = torch.randn(self.batch_size, self.T, self.d)

        # Test using our actual implementation
        from source.delta.core.base.types import SequenceStates
        
        # Validate tensor properties
        assert X.dim() == 3, f"Expected 3D tensor, got {X.dim()}D"
        assert X.shape == (self.batch_size, self.T, self.d), f"Expected shape {(self.batch_size, self.T, self.d)}, got {X.shape}"
        assert X.dtype == torch.float32, f"Expected float32, got {X.dtype}"
        
        # Test type annotation compatibility
        states: SequenceStates = X
        assert states is not None, "SequenceStates type annotation should work"

    def test_hidden_state_structure(self):
        """
        Test h ∈ ℝ^d - hidden state dimensions

        Tests the hidden state structure for DELTA blocks.
        Now tests our actual implementation.
        """
        # Create test hidden state
        h = torch.randn(self.batch_size, self.d)

        # Test using our actual implementation
        from source.delta.core.base.types import StateVector
        
        # Validate tensor properties
        assert h.dim() == 2, f"Expected 2D tensor, got {h.dim()}D"
        assert h.shape == (self.batch_size, self.d), f"Expected shape {(self.batch_size, self.d)}, got {h.shape}"
        assert h.dtype == torch.float32, f"Expected float32, got {h.dtype}"
        
        # Test type annotation compatibility
        state: StateVector = h
        assert state is not None, "StateVector type annotation should work"

    def test_delta_config_structure(self):
        """
        Test DELTAConfig data structure.
        
        Tests our implemented DELTAConfig with all required fields.
        """
        from source.delta.core.base.types import DELTAConfig
        
        # Test default configuration
        config = DELTAConfig()
        
        # Validate basic fields
        assert config.d_model > 0, "Model dimension should be positive"
        assert config.n_layers > 0, "Number of layers should be positive"
        assert config.n_attractors > 0, "Number of attractors should be positive"
        assert config.max_seq_length > 0, "Max sequence length should be positive"
        assert config.vocab_size > 0, "Vocabulary size should be positive"
        
        # Validate mutation parameters (added in Phase 1)
        assert 0.0 <= config.mutation_rate <= 1.0, "Mutation rate should be between 0 and 1"
        assert config.essence_pool_size > 0, "Essence pool size should be positive"
        assert config.essence_extraction_method in ["mean", "max", "learned"], "Invalid essence extraction method"
        
        # Test custom configuration
        custom_config = DELTAConfig(
            d_model=256,
            n_layers=6,
            n_attractors=4,
            mutation_rate=0.2,
            essence_pool_size=50
        )
        
        assert custom_config.d_model == 256, "Custom d_model not set correctly"
        assert custom_config.n_layers == 6, "Custom n_layers not set correctly"
        assert custom_config.mutation_rate == 0.2, "Custom mutation_rate not set correctly"

    def test_training_config_structure(self):
        """
        Test TrainingConfig data structure.
        
        Tests our implemented TrainingConfig with dual-objective parameters.
        """
        from source.delta.models.training_config import TrainingConfig
        
        # Test default configuration
        config = TrainingConfig()
        
        # Validate learning rates
        assert config.learning_rate > 0, "Learning rate should be positive"
        assert config.thesis_lr > 0, "Thesis learning rate should be positive"
        assert config.antithesis_lr > 0, "Antithesis learning rate should be positive"
        
        # Validate training parameters
        assert config.warmup_steps >= 0, "Warmup steps should be non-negative"
        assert config.max_steps > 0, "Max steps should be positive"
        assert config.batch_size > 0, "Batch size should be positive"
        
        # Validate dual-objective loss weights
        assert config.global_loss_weight >= 0, "Global loss weight should be non-negative"
        assert config.local_loss_weight >= 0, "Local loss weight should be non-negative"
        assert config.tension_loss_weight >= 0, "Tension loss weight should be non-negative"
        
        # Test to_dict and from_dict methods
        config_dict = config.to_dict()
        assert isinstance(config_dict, dict), "to_dict should return a dictionary"
        
        recreated_config = TrainingConfig.from_dict(config_dict)
        assert recreated_config.learning_rate == config.learning_rate, "from_dict should recreate config correctly"

    def test_kappa_cache_structure(self):
        """
        Test κ-Cache structure as implemented.
        
        Tests our actual KappaCache implementation.
        """
        from source.delta.core.base.types import KappaCache
        
        # Create test cache
        cache = KappaCache(
            cache={},
            seq_length=self.T,
            batch_size=self.batch_size,
            d_model=self.d
        )
        
        # Test cache properties
        assert cache.seq_length == self.T, f"Expected seq_length {self.T}, got {cache.seq_length}"
        assert cache.batch_size == self.batch_size, f"Expected batch_size {self.batch_size}, got {cache.batch_size}"
        assert cache.d_model == self.d, f"Expected d_model {self.d}, got {cache.d_model}"
        
        # Test layer cache operations
        test_kappa = torch.randn(self.batch_size, self.T, self.d)
        cache.set_layer_cache(0, test_kappa)
        retrieved = cache.get_cache_for_layer(0)
        
        assert torch.equal(test_kappa, retrieved), "Cache should store and retrieve tensors correctly"
        
        # Test cache clearing
        cache.clear()
        try:
            retrieved_after_clear = cache.get_cache_for_layer(0)
            assert False, "Should have raised RuntimeError after clearing"
        except RuntimeError:
            pass  # Expected behavior after clearing

    def test_attractor_state_manager(self):
        """
        Test AttractorStateManager implementation.
        
        Tests our implemented AttractorStateManager for semantic attractors.
        """
        from source.delta.models.state_manager import AttractorStateManager
        from source.delta.core.base.types import StateVector
        
        # Create state manager
        manager = AttractorStateManager(max_layers=self.N, max_attractors_per_layer=self.k)
        
        # Create test states
        test_states = {}
        for layer_idx in range(3):  # Test first 3 layers
            layer_states = []
            for attractor_idx in range(self.k):
                state = torch.randn(self.batch_size, self.d // 2)  # Semantic hidden size
                layer_states.append(state)
            test_states[layer_idx] = layer_states
        
        # Test saving and loading states
        manager.save_states(test_states)
        
        for layer_idx in range(3):
            loaded_states = manager.load_states(layer_idx)
            assert len(loaded_states) == self.k, f"Expected {self.k} states, got {len(loaded_states)}"
            
            for i, loaded_state in enumerate(loaded_states):
                original_state = test_states[layer_idx][i]
                assert torch.equal(loaded_state, original_state), f"State {i} in layer {layer_idx} not preserved"
        
        # Test individual state access
        single_state = manager.get_state(0, 0)
        assert torch.equal(single_state, test_states[0][0]), "Individual state access should work"
        """
        Test BaseAttractor initialization strategies.
        
        Tests the xavier_init and orthogonal_init methods we added.
        """

        import torch.nn as nn
        

    def test_math_operations_integration(self):
        """
        Test integration with math operations.
        
        Tests that our data structures work with the math operations.
        """
        from source.delta.math.fft_ops import circular_convolution_fft
        from source.delta.math.activations import gelu
        from source.delta.components.normalization import LayerNorm
        
        # Test FFT operations with our tensor structures
        thesis = torch.randn(self.batch_size, self.d)
        antithesis = torch.randn(self.batch_size, self.d)
        
        # Test circular convolution (used in synthesis)
        synthesis = circular_convolution_fft(thesis, antithesis)
        assert synthesis.shape == thesis.shape, "FFT convolution should preserve shape"
        assert synthesis.dtype == torch.float32, "FFT convolution should return real tensor"
        
        # Test activations
        activated = gelu(thesis)
        assert activated.shape == thesis.shape, "GELU should preserve shape"
        
        # Test normalization
        norm_layer = LayerNorm(self.d)
        normalized = norm_layer(thesis)
        assert normalized.shape == thesis.shape, "LayerNorm should preserve shape"
